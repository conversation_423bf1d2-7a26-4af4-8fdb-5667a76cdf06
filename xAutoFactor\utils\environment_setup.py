#!/usr/bin/env python3
"""
环境设置模块
负责创建必要的目录结构和基本配置
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger
from .path_manager import get_path_manager


class EnvironmentSetup:
    """环境设置管理器"""

    def __init__(self):
        self.path_manager = get_path_manager()

    def load_config(self, filename: str) -> Optional[Dict[str, Any]]:
        """从文件加载配置"""
        try:
            # 使用路径管理器获取配置文件路径
            config_path = self.path_manager.get_config_path(filename)

            if config_path.exists():
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                logger.info(f"加载配置文件: {config_path}")
                return config

            # 如果找不到配置文件，尝试创建默认配置
            logger.error(f"配置文件 {filename} 不存在，尝试创建默认配置")
            return None

        except FileNotFoundError:
            logger.error(f"配置文件 {filename} 不存在")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"配置文件 {filename} 格式错误: {e}")
            return None
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return None

def load_config(filename: str) -> Optional[Dict[str, Any]]:
    """从文件加载配置（向后兼容）"""
    env_setup = EnvironmentSetup()
    return env_setup.load_config(filename)
