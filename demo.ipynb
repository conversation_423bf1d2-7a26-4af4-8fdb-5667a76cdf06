{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9b5615c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'generation': 1, 'population': [['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['price_falling', 'not ma5_gt_ma10'], ['close_gt_ma10', 'close_gt_ma10'], ['(high_volatility) or (ma5_gt_ma20)', '(ma5_gt_ma60) and (rsi_bullish)'], ['(macd_hist_positive) and (not ma_bullish)', '((not cci_oversold) and (volume_above_avg)) and (rsi_rising)'], ['(high_volatility) or (ma5_gt_ma20)', '(ma5_gt_ma60) and (rsi_bullish)'], ['(close_above_bb_upper) and (price_gap_up)', '(cci_bullish) and (price_rising)'], ['close_gt_ma10', 'close_gt_ma10'], ['(rsi_overbought) and (ma5_gt_ma10)', '(rsi_rising) and (macd_bearish)'], ['(rsi_overbought) and (ma5_gt_ma10)', '(rsi_rising) and (macd_bearish)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['strong_breakout', 'bb_squeeze'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(macd_positive) and (not ma5_gt_ma20)', '(macd_golden_cross) and (macd_positive)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(macd_hist_positive) and (not ma_bullish)', '((not cci_oversold) and (volume_above_avg)) and (rsi_rising)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(high_volatility) or (ma5_gt_ma20)', '(ma5_gt_ma60) and (rsi_bullish)']], 'halloffame': <deap.tools.support.HallOfFame object at 0x00000298F583EDB0>, 'logbook': [{'gen': 0, 'nevals': 20, 'avg': np.float64(0.1417156361497217), 'min': np.float64(-0.5192240060157292), 'max': np.float64(0.8468833403246041), 'std': np.float64(0.3245727305297713)}, {'gen': 1, 'nevals': 17, 'avg': np.float64(0.4574756303139469), 'min': np.float64(-0.0073), 'max': np.float64(0.8468833403246041), 'std': np.float64(0.27238547302201227)}], 'config': {'population_size': 20, 'generations': 10, 'max_depth': 3, 'mutation_rate': 0.2, 'crossover_rate': 0.8, 'early_stopping': True, 'patience': 10, 'min_improvement': 1e-06}, 'evaluation_results': {'(volume_above_avg) and (consecutive_up_2)|((close_gt_ma5) and (overbought_pullback)) or (kdj_bearish)': {'fitness': np.float64(-0.17535247776480403), 'buy_signal': '(volume_above_avg) and (consecutive_up_2)', 'sell_signal': '((close_gt_ma5) and (overbought_pullback)) or (kdj_bearish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 47, 'total_return': 0.0, 'annual_return': 0.0, 'volatility': np.float64(0.12206934275474418), 'sharpe_ratio': np.float64(-0.1638413015803897), 'max_drawdown': 0.0, 'calmar_ratio': 0.0, 'win_rate': 0.425531914893617, 'avg_win': np.float64(0.03196537858022761), 'avg_loss': np.float64(-0.023249139709568885), 'profit_factor': np.float64(1.018448790078481), 'avg_holding_days': np.float64(4.957446808510638), 'total_profit': np.float64(0.6393075716045521), 'total_loss': np.float64(0.6277267721583599), 'recovery_factor': 0.0}}, 'MSFT': {'train_metrics': {'total_trades': 45, 'total_return': np.float64(-0.024386417457140874), 'annual_return': np.float64(-0.0065482129697836955), 'volatility': np.float64(0.13538735861863593), 'sharpe_ratio': np.float64(-0.19609078159627646), 'max_drawdown': np.float64(-0.2045213890118611), 'calmar_ratio': np.float64(-0.032017252578916994), 'win_rate': 0.35555555555555557, 'avg_win': np.float64(0.028670253988578746), 'avg_loss': np.float64(-0.023149819006329283), 'profit_factor': np.float64(0.6832913536726861), 'avg_holding_days': np.float64(5.533333333333333), 'total_profit': np.float64(0.45872406381726), 'total_loss': np.float64(0.6713447511835492), 'recovery_factor': np.float64(-0.1192365139654249)}}, 'GOOGL': {'train_metrics': {'total_trades': 42, 'total_return': np.float64(-0.2079337981564331), 'annual_return': np.float64(-0.060146685035844416), 'volatility': np.float64(0.1537287056598114), 'sharpe_ratio': np.float64(-0.5213514593247291), 'max_drawdown': np.float64(-0.2888357664620271), 'calmar_ratio': np.float64(-0.20823835556304568), 'win_rate': 0.42857142857142855, 'avg_win': np.float64(0.04056478398077203), 'avg_loss': np.float64(-0.025905515708678353), 'profit_factor': np.float64(1.1744058032933546), 'avg_holding_days': np.float64(5.714285714285714), 'total_profit': np.float64(0.7301661116538964), 'total_loss': np.float64(0.6217323770082804), 'recovery_factor': np.float64(-0.7199032194088398)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.29376118083379843), 'avg_calmar': np.float64(-0.08008520271398756), 'avg_return': np.float64(-0.022231632668542705)}}, '(((macd_hist_negative) and (not rsi_overbought)) and (rsi_falling)) and (close_below_bb_lower)|(((weak_breakdown) and (rsi_overbought)) and (ma_bearish)) and (rsi_extreme_oversold)': {'fitness': 0.0, 'buy_signal': '(((macd_hist_negative) and (not rsi_overbought)) and (rsi_falling)) and (close_below_bb_lower)', 'sell_signal': '(((weak_breakdown) and (rsi_overbought)) and (ma_bearish)) and (rsi_extreme_oversold)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '((macd_bearish) and (kdj_overbought)) and (ma5_gt_ma10)|(cci_overbought) and (ma10_gt_ma20)': {'fitness': np.float64(-0.011983796649101782), 'buy_signal': '((macd_bearish) and (kdj_overbought)) and (ma5_gt_ma10)', 'sell_signal': '(cci_overbought) and (ma10_gt_ma20)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(0.0782207236226975), 'annual_return': np.float64(0.02024300593798456), 'volatility': np.float64(0.08053772354773775), 'sharpe_ratio': np.float64(0.003017293353722871), 'max_drawdown': np.float64(-0.2564191571117785), 'calmar_ratio': np.float64(0.07894498276179969), 'win_rate': 0.875, 'avg_win': np.float64(0.013144038985181156), 'avg_loss': np.float64(-0.028474299213547723), 'profit_factor': np.float64(3.231274357491182), 'avg_holding_days': np.float64(14.625), 'total_profit': np.float64(0.09200827289626809), 'total_loss': np.float64(0.028474299213547723), 'recovery_factor': np.float64(0.30505023300033485)}}, 'MSFT': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(0.06426425947588887), 'annual_return': np.float64(0.016712025358424976), 'volatility': np.float64(0.06359676494785432), 'sharpe_ratio': np.float64(-0.051700344259192656), 'max_drawdown': np.float64(-0.1615966909139663), 'calmar_ratio': np.float64(0.10341811619968393), 'win_rate': 0.6, 'avg_win': np.float64(0.023663313111560075), 'avg_loss': np.float64(-0.01639085778673795), 'profit_factor': np.float64(2.1655346003953215), 'avg_holding_days': np.float64(20.0), 'total_profit': np.float64(0.07098993933468023), 'total_loss': np.float64(0.0327817155734759), 'recovery_factor': np.float64(0.39768301635646125)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(0.02867125015770733), 'annual_return': np.float64(0.007550555049435026), 'volatility': np.float64(0.06084078954497826), 'sharpe_ratio': np.float64(-0.20462332990207782), 'max_drawdown': np.float64(-0.08361092296688313), 'calmar_ratio': np.float64(0.09030584499618156), 'win_rate': 0.6, 'avg_win': np.float64(0.02294745052567609), 'avg_loss': np.float64(-0.010460268781902801), 'profit_factor': np.float64(3.2906588259056826), 'avg_holding_days': np.float64(23.0), 'total_profit': np.float64(0.06884235157702827), 'total_loss': np.float64(0.020920537563805602), 'recovery_factor': np.float64(0.342912733651601)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.08443546026918254), 'avg_calmar': np.float64(0.09088964798588839), 'avg_return': np.float64(0.014835195448614854)}}, '(close_near_bb_lower) and (rsi_falling)|(cci_bearish) or (ma_bullish)': {'fitness': 0.0, 'buy_signal': '(close_near_bb_lower) and (rsi_falling)', 'sell_signal': '(cci_bearish) or (ma_bullish)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, 'strong_breakout|bb_squeeze': {'fitness': np.float64(0.25756494954584097), 'buy_signal': 'strong_breakout', 'sell_signal': 'bb_squeeze', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(0.047993548297503796), 'annual_return': np.float64(0.012552375338259703), 'volatility': np.float64(0.09018939093395958), 'sharpe_ratio': np.float64(-0.08257761344894499), 'max_drawdown': np.float64(-0.07456926700168978), 'calmar_ratio': np.float64(0.16833175171180453), 'win_rate': 0.75, 'avg_win': np.float64(0.06421323007727876), 'avg_loss': np.float64(-0.005352618646596558), 'profit_factor': np.float64(35.989802926521854), 'avg_holding_days': np.float64(19.125), 'total_profit': np.float64(0.38527938046367255), 'total_loss': np.float64(0.010705237293193117), 'recovery_factor': np.float64(0.643610299889581)}}, 'MSFT': {'train_metrics': {'total_trades': 11, 'total_return': np.float64(0.4264977341166063), 'annual_return': np.float64(0.099137622442099), 'volatility': np.float64(0.09957111326775338), 'sharpe_ratio': np.float64(0.794784951628417), 'max_drawdown': np.float64(-0.07381656621702605), 'calmar_ratio': np.float64(1.3430267421357316), 'win_rate': 0.5454545454545454, 'avg_win': np.float64(0.052607763874638856), 'avg_loss': np.float64(-0.055891021145101624), 'profit_factor': np.float64(1.1295073046826838), 'avg_holding_days': np.float64(22.0), 'total_profit': np.float64(0.3156465832478331), 'total_loss': np.float64(0.27945510572550813), 'recovery_factor': np.float64(5.777805118469912)}}, 'GOOGL': {'train_metrics': {'total_trades': 10, 'total_return': np.float64(0.013326797674263258), 'annual_return': np.float64(0.003529097361871125), 'volatility': np.float64(0.12364483610214994), 'sharpe_ratio': np.float64(-0.1332114074260355), 'max_drawdown': np.float64(-0.1569530331413693), 'calmar_ratio': np.float64(0.022485053593659633), 'win_rate': 0.5, 'avg_win': np.float64(0.06665467347577889), 'avg_loss': np.float64(-0.0572816526781029), 'profit_factor': np.float64(1.1636304184578639), 'avg_holding_days': np.float64(19.0), 'total_profit': np.float64(0.33327336737889446), 'total_loss': np.float64(0.2864082633905145), 'recovery_factor': np.float64(0.08490946245211882)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.19299864358447882), 'avg_calmar': np.float64(0.5112811824803986), 'avg_return': np.float64(0.03840636504740994)}}, 'price_falling|not ma5_gt_ma10': {'fitness': np.float64(0.3527945431546566), 'buy_signal': 'price_falling', 'sell_signal': 'not ma5_gt_ma10', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 39, 'total_return': np.float64(0.020267957129627145), 'annual_return': np.float64(0.005353725392860165), 'volatility': np.float64(0.21307215440161292), 'sharpe_ratio': np.float64(-0.06873856721574954), 'max_drawdown': np.float64(-0.21460688527097888), 'calmar_ratio': np.float64(0.024946661828208105), 'win_rate': 0.5641025641025641, 'avg_win': np.float64(0.0649381035220819), 'avg_loss': np.float64(-0.03551799688389613), 'profit_factor': np.float64(2.366055327082974), 'avg_holding_days': np.float64(20.307692307692307), 'total_profit': np.float64(1.4286382774858017), 'total_loss': np.float64(0.6038059470262344), 'recovery_factor': np.float64(0.09444225008920516)}}, 'MSFT': {'train_metrics': {'total_trades': 47, 'total_return': np.float64(1.0316274152923488), 'annual_return': np.float64(0.20758685894676687), 'volatility': np.float64(0.2167360980671075), 'sharpe_ratio': np.float64(0.8655081484796537), 'max_drawdown': np.float64(-0.2539608265958145), 'calmar_ratio': np.float64(0.8173971621109384), 'win_rate': 0.44680851063829785, 'avg_win': np.float64(0.06338237840179758), 'avg_loss': np.float64(-0.030425373126044156), 'profit_factor': np.float64(1.6825910159357522), 'avg_holding_days': np.float64(17.595744680851062), 'total_profit': np.float64(1.3310299464377493), 'total_loss': np.float64(0.7910597012771481), 'recovery_factor': np.float64(4.06215174647471)}}, 'GOOGL': {'train_metrics': {'total_trades': 53, 'total_return': np.float64(0.575850345290909), 'annual_return': np.float64(0.12865035396369895), 'volatility': np.float64(0.22098561751308712), 'sharpe_ratio': np.float64(0.49166255789141794), 'max_drawdown': np.float64(-0.4143496547597064), 'calmar_ratio': np.float64(0.3104874168129984), 'win_rate': 0.6037735849056604, 'avg_win': np.float64(0.03226189178012737), 'avg_loss': np.float64(-0.030881624549753156), 'profit_factor': np.float64(1.591916832984851), 'avg_holding_days': np.float64(14.660377358490566), 'total_profit': np.float64(1.0323805369640757), 'total_loss': np.float64(0.6485141155448163), 'recovery_factor': np.float64(1.3897690964044884)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.42947737971844074), 'avg_calmar': np.float64(0.38427708025071494), 'avg_return': np.float64(0.11386364610110866)}}, '(rsi_falling) and (rsi_overbought)|(macd_hist_positive) or (oversold_bounce)': {'fitness': np.float64(0.8543833403246041), 'buy_signal': '(rsi_falling) and (rsi_overbought)', 'sell_signal': '(macd_hist_positive) or (oversold_bounce)', 'detailed_results': {'MSFT': {'train_metrics': {'total_trades': 1, 'total_return': np.float64(0.3749659200198867), 'annual_return': np.float64(0.08842865148621959), 'volatility': np.float64(0.04500553158871368), 'sharpe_ratio': np.float64(1.5204497996281832), 'max_drawdown': np.float64(-0.3469027757991371), 'calmar_ratio': np.float64(0.2549090340442285), 'win_rate': 1.0, 'avg_win': np.float64(0.018848245906615747), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(42.0), 'total_profit': np.float64(0.018848245906615747), 'total_loss': 0, 'recovery_factor': np.float64(1.0808962803947082)}}}, 'avg_metrics': {'avg_sharpe': np.float64(1.5204497996281832), 'avg_calmar': np.float64(0.2549090340442285), 'avg_return': np.float64(0.08842865148621959)}}, '(not overbought_pullback) and (close_near_bb_lower)|((strong_breakout) and (price_gap_down)) and (volume_dry)': {'fitness': 0.0, 'buy_signal': '(not overbought_pullback) and (close_near_bb_lower)', 'sell_signal': '((strong_breakout) and (price_gap_down)) and (volume_dry)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(price_falling) and (high_volatility)|(not strong_breakout) or (rsi_extreme_oversold)': {'fitness': np.float64(-0.5108240060157292), 'buy_signal': '(price_falling) and (high_volatility)', 'sell_signal': '(not strong_breakout) or (rsi_extreme_oversold)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 2, 'total_return': np.float64(0.01880757026304858), 'annual_return': np.float64(0.004970590802615638), 'volatility': np.float64(0.016238403004110797), 'sharpe_ratio': np.float64(-0.925547247077168), 'max_drawdown': np.float64(-0.06761664250979611), 'calmar_ratio': np.float64(0.07351135191155805), 'win_rate': 1.0, 'avg_win': np.float64(0.0050739707251652305), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(4.5), 'total_profit': np.float64(0.010147941450330461), 'total_loss': 0, 'recovery_factor': np.float64(0.27815001699210057)}}, 'MSFT': {'train_metrics': {'total_trades': 1, 'total_return': np.float64(0.010133370987345458), 'annual_return': np.float64(0.0026865568308336307), 'volatility': np.float64(0.012888122691182538), 'sharpe_ratio': np.float64(-1.3433642419474663), 'max_drawdown': np.float64(-0.018764634283420123), 'calmar_ratio': np.float64(0.14317128648797559), 'win_rate': 0.0, 'avg_win': 0.0, 'avg_loss': np.float64(-0.03007828991399924), 'profit_factor': np.float64(0.0), 'avg_holding_days': np.float64(5.0), 'total_profit': 0, 'total_loss': np.float64(0.03007828991399924), 'recovery_factor': np.float64(0.540024965810232)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(-0.030021161412461028), 'annual_return': np.float64(-0.008078300333134503), 'volatility': np.float64(0.036081535467552106), 'sharpe_ratio': np.float64(-0.7781902840134156), 'max_drawdown': np.float64(-0.03282007789400243), 'calmar_ratio': np.float64(-0.2461389750269526), 'win_rate': 0.4, 'avg_win': np.float64(0.018617423888860035), 'avg_loss': np.float64(-0.024657588881700023), 'profit_factor': np.float64(0.5033588638959807), 'avg_holding_days': np.float64(3.6), 'total_profit': np.float64(0.03723484777772007), 'total_loss': np.float64(0.07397276664510007), 'recovery_factor': np.float64(-0.9147193833426922)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-1.0157005910126833), 'avg_calmar': np.float64(-0.009818778875806317), 'avg_return': np.float64(-0.00014038423322841145)}}, 'close_gt_ma10|close_gt_ma10': {'fitness': 0.0, 'buy_signal': 'close_gt_ma10', 'sell_signal': 'close_gt_ma10', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(close_gt_ma20) and (not ma5_gt_ma20)|(not cci_overbought) and (close_gt_ma20)': {'fitness': np.float64(-0.16791381234614236), 'buy_signal': '(close_gt_ma20) and (not ma5_gt_ma20)', 'sell_signal': '(not cci_overbought) and (close_gt_ma20)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(-0.03785091995776191), 'annual_return': np.float64(-0.010215300598098498), 'volatility': np.float64(0.056817365163021126), 'sharpe_ratio': np.float64(-0.5317969341134416), 'max_drawdown': np.float64(-0.05994894982367066), 'calmar_ratio': np.float64(-0.1703999924626706), 'win_rate': 0.5, 'avg_win': np.float64(0.031899677469663164), 'avg_loss': np.float64(-0.012768870126951434), 'profit_factor': np.float64(2.49823807059734), 'avg_holding_days': np.float64(6.25), 'total_profit': np.float64(0.12759870987865266), 'total_loss': np.float64(0.051075480507805734), 'recovery_factor': np.float64(-0.6313858719642923)}}, 'MSFT': {'train_metrics': {'total_trades': 15, 'total_return': np.float64(0.07537817533746138), 'annual_return': np.float64(0.0195265740020516), 'volatility': np.float64(0.08385425649692163), 'sharpe_ratio': np.float64(-0.0056458195174121), 'max_drawdown': np.float64(-0.08678252153619925), 'calmar_ratio': np.float64(0.22500583823099168), 'win_rate': 0.3333333333333333, 'avg_win': np.float64(0.024307864858775154), 'avg_loss': np.float64(-0.01602564015789981), 'profit_factor': np.float64(0.7584054246592027), 'avg_holding_days': np.float64(7.8), 'total_profit': np.float64(0.12153932429387576), 'total_loss': np.float64(0.1602564015789981), 'recovery_factor': np.float64(0.8685870611171304)}}, 'GOOGL': {'train_metrics': {'total_trades': 11, 'total_return': np.float64(-0.041855069072135986), 'annual_return': np.float64(-0.011313101243020318), 'volatility': np.float64(0.06732948587317868), 'sharpe_ratio': np.float64(-0.46507263254618414), 'max_drawdown': np.float64(-0.1838021169960566), 'calmar_ratio': np.float64(-0.06155044037530338), 'win_rate': 0.45454545454545453, 'avg_win': np.float64(0.0590850938104716), 'avg_loss': np.float64(-0.01009618069443435), 'profit_factor': np.float64(4.876851917134947), 'avg_holding_days': np.float64(7.909090909090909), 'total_profit': np.float64(0.295425469052358), 'total_loss': np.float64(0.0605770841666061), 'recovery_factor': np.float64(-0.227718101163296)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.3341717953923459), 'avg_calmar': np.float64(-0.0023148648689940965), 'avg_return': np.float64(-0.0006672759463557387)}}, '(close_above_bb_upper) and (price_gap_up)|(cci_bullish) and (price_rising)': {'fitness': 0.0, 'buy_signal': '(close_above_bb_upper) and (price_gap_up)', 'sell_signal': '(cci_bullish) and (price_rising)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(high_volatility) or (ma5_gt_ma20)|(ma5_gt_ma60) and (rsi_bullish)': {'fitness': np.float64(0.6149625560052501), 'buy_signal': '(high_volatility) or (ma5_gt_ma20)', 'sell_signal': '(ma5_gt_ma60) and (rsi_bullish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 13, 'total_return': np.float64(0.2487534816405501), 'annual_return': np.float64(0.06089594461610437), 'volatility': np.float64(0.24875198993938175), 'sharpe_ratio': np.float64(0.1644044923060526), 'max_drawdown': np.float64(-0.05677007468712703), 'calmar_ratio': np.float64(1.072676845181479), 'win_rate': 0.8461538461538461, 'avg_win': np.float64(0.0919353549494769), 'avg_loss': np.float64(-0.024762362334475346), 'profit_factor': np.float64(20.41987938760352), 'avg_holding_days': np.float64(81.92307692307692), 'total_profit': np.float64(1.0112889044442457), 'total_loss': np.float64(0.04952472466895069), 'recovery_factor': np.float64(4.381771259091835)}}, 'MSFT': {'train_metrics': {'total_trades': 18, 'total_return': np.float64(1.3277079774056446), 'annual_return': np.float64(0.25210558047538445), 'volatility': np.float64(0.23788402027099995), 'sharpe_ratio': np.float64(0.9757090039548153), 'max_drawdown': np.float64(-0.23937091041204842), 'calmar_ratio': np.float64(1.053200574962993), 'win_rate': 0.8333333333333334, 'avg_win': np.float64(0.057790065225418415), 'avg_loss': np.float64(-0.0500660841685446), 'profit_factor': np.float64(5.771378587435706), 'avg_holding_days': np.float64(49.666666666666664), 'total_profit': np.float64(0.8668509783812762), 'total_loss': np.float64(0.1501982525056338), 'recovery_factor': np.float64(5.546655502626255)}}, 'GOOGL': {'train_metrics': {'total_trades': 14, 'total_return': np.float64(0.926367569907905), 'annual_return': np.float64(0.19061152528506198), 'volatility': np.float64(0.266044928892038), 'sharpe_ratio': np.float64(0.641288394391072), 'max_drawdown': np.float64(-0.2651053433044748), 'calmar_ratio': np.float64(0.7190029552370949), 'win_rate': 0.8571428571428571, 'avg_win': np.float64(0.0752317827434058), 'avg_loss': np.float64(-0.11902630028902225), 'profit_factor': np.float64(3.792360977064381), 'avg_holding_days': np.float64(55.285714285714285), 'total_profit': np.float64(0.9027813929208697), 'total_loss': np.float64(0.2380526005780445), 'recovery_factor': np.float64(3.49433760316165)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.5938006302173133), 'avg_calmar': np.float64(0.9482934584605224), 'avg_return': np.float64(0.1678710167921836)}}, '(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)|((strong_breakout) and (price_rising)) and (ma20_gt_ma60)': {'fitness': np.float64(0.7039741954936695), 'buy_signal': '(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', 'sell_signal': '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 4, 'total_return': np.float64(0.7806768944190625), 'annual_return': np.float64(0.16595442385491976), 'volatility': np.float64(0.2920939669875619), 'sharpe_ratio': np.float64(0.49968311690989115), 'max_drawdown': np.float64(-0.33905140147756246), 'calmar_ratio': np.float64(0.48946685703613646), 'win_rate': 1.0, 'avg_win': np.float64(0.3510338571056776), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(292.5), 'total_profit': np.float64(1.4041354284227103), 'total_loss': 0, 'recovery_factor': np.float64(2.302532568858075)}}, 'MSFT': {'train_metrics': {'total_trades': 4, 'total_return': np.float64(2.357899566840758), 'annual_return': np.float64(0.3803475501727871), 'volatility': np.float64(0.2843912270981787), 'sharpe_ratio': np.float64(1.2670839176357096), 'max_drawdown': np.float64(-0.3091280580323244), 'calmar_ratio': np.float64(1.2303883141303709), 'win_rate': 1.0, 'avg_win': np.float64(0.1954686774658426), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(285.25), 'total_profit': np.float64(0.7818747098633704), 'total_loss': 0, 'recovery_factor': np.float64(7.627581856688664)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(1.2687617875149382), 'annual_return': np.float64(0.24358840980522878), 'volatility': np.float64(0.3123704516597597), 'sharpe_ratio': np.float64(0.71577964118311), 'max_drawdown': np.float64(-0.3714849600694853), 'calmar_ratio': np.float64(0.6557154016670451), 'win_rate': 0.8, 'avg_win': np.float64(0.2853823357033271), 'avg_loss': np.float64(-0.1682626430826989), 'profit_factor': np.float64(6.784211408424516), 'avg_holding_days': np.float64(242.0), 'total_profit': np.float64(1.1415293428133084), 'total_loss': np.float64(0.1682626430826989), 'recovery_factor': np.float64(3.415378612575916)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.8275155585762369), 'avg_calmar': np.float64(0.7918568576111841), 'avg_return': np.float64(0.26329679461097855)}}, '(not macd_hist_negative) or (rsi_falling)|(ma5_gt_ma20) and (close_below_bb_lower)': {'fitness': 0.0, 'buy_signal': '(not macd_hist_negative) or (rsi_falling)', 'sell_signal': '(ma5_gt_ma20) and (close_below_bb_lower)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(macd_death_cross) and (macd_golden_cross)|(close_gt_ma60) or (price_gap_down)': {'fitness': 0.0, 'buy_signal': '(macd_death_cross) and (macd_golden_cross)', 'sell_signal': '(close_gt_ma60) or (price_gap_down)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(macd_positive) and (not ma5_gt_ma20)|(macd_golden_cross) and (macd_positive)': {'fitness': np.float64(0.5380866750472469), 'buy_signal': '(macd_positive) and (not ma5_gt_ma20)', 'sell_signal': '(macd_golden_cross) and (macd_positive)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 7, 'total_return': np.float64(1.0992347541580862), 'annual_return': np.float64(0.21815220718794204), 'volatility': np.float64(0.2506716897169419), 'sharpe_ratio': np.float64(0.790484986205244), 'max_drawdown': np.float64(-0.44320055525533303), 'calmar_ratio': np.float64(0.49222006741905366), 'win_rate': 0.8571428571428571, 'avg_win': np.float64(0.08266514492003213), 'avg_loss': np.float64(-0.09025219654760186), 'profit_factor': np.float64(5.495609951815317), 'avg_holding_days': np.float64(127.85714285714286), 'total_profit': np.float64(0.4959908695201928), 'total_loss': np.float64(0.09025219654760186), 'recovery_factor': np.float64(2.480219713454114)}}, 'MSFT': {'train_metrics': {'total_trades': 12, 'total_return': np.float64(0.45211054878984136), 'annual_return': np.float64(0.10435494319483452), 'volatility': np.float64(0.22869789246246242), 'sharpe_ratio': np.float64(0.36884879998918313), 'max_drawdown': np.float64(-0.2982477138495167), 'calmar_ratio': np.float64(0.3498935225618784), 'win_rate': 0.9166666666666666, 'avg_win': np.float64(0.07371721685746092), 'avg_loss': np.float64(-0.09478437739349209), 'profit_factor': np.float64(8.555095340930581), 'avg_holding_days': np.float64(60.5), 'total_profit': np.float64(0.8108893854320701), 'total_loss': np.float64(0.09478437739349209), 'recovery_factor': np.float64(1.5158894026525795)}}, 'GOOGL': {'train_metrics': {'total_trades': 12, 'total_return': np.float64(1.2270546007985756), 'annual_return': np.float64(0.2374634912506337), 'volatility': np.float64(0.26878094702699673), 'sharpe_ratio': np.float64(0.8090733128817772), 'max_drawdown': np.float64(-0.26839284036821554), 'calmar_ratio': np.float64(0.8847609009422569), 'win_rate': 0.9166666666666666, 'avg_win': np.float64(0.05522141733932915), 'avg_loss': np.float64(-0.2281054858375987), 'profit_factor': np.float64(2.662959150246341), 'avg_holding_days': np.float64(66.66666666666667), 'total_profit': np.float64(0.6074355907326207), 'total_loss': np.float64(0.2281054858375987), 'recovery_factor': np.float64(4.571860408478653)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.6561356996920681), 'avg_calmar': np.float64(0.5756248303077296), 'avg_return': np.float64(0.18665688054447008)}}, '(((cci_bearish) and (not close_gt_ma10)) and (not rsi_falling)) and (price_falling)|((overbought_pullback) or (price_falling)) or (golden_cross_buy)': {'fitness': 0.0, 'buy_signal': '(((cci_bearish) and (not close_gt_ma10)) and (not rsi_falling)) and (price_falling)', 'sell_signal': '((overbought_pullback) or (price_falling)) or (golden_cross_buy)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(rsi_overbought) and (ma5_gt_ma10)|(rsi_rising) and (macd_bearish)': {'fitness': np.float64(0.28500634042642065), 'buy_signal': '(rsi_overbought) and (ma5_gt_ma10)', 'sell_signal': '(rsi_rising) and (macd_bearish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 13, 'total_return': np.float64(0.5330810376601862), 'annual_return': np.float64(0.12041656972511738), 'volatility': np.float64(0.11946789448711578), 'sharpe_ratio': np.float64(0.8405318446115828), 'max_drawdown': np.float64(-0.43628380631447056), 'calmar_ratio': np.float64(0.27600513239843216), 'win_rate': 0.5384615384615384, 'avg_win': np.float64(0.06932237925193112), 'avg_loss': np.float64(-0.019632320111046774), 'profit_factor': np.float64(4.119539039185968), 'avg_holding_days': np.float64(19.53846153846154), 'total_profit': np.float64(0.48525665476351787), 'total_loss': np.float64(0.11779392066628065), 'recovery_factor': np.float64(1.221867577812285)}}, 'MSFT': {'train_metrics': {'total_trades': 14, 'total_return': np.float64(0.4107028719418504), 'annual_return': np.float64(0.09588585336515743), 'volatility': np.float64(0.09871623075522201), 'sharpe_ratio': np.float64(0.7687272172427746), 'max_drawdown': np.float64(-0.14824243869409903), 'calmar_ratio': np.float64(0.6468178357684713), 'win_rate': 0.5, 'avg_win': np.float64(0.02434437656878889), 'avg_loss': np.float64(-0.03658780461763278), 'profit_factor': np.float64(0.6653686063759232), 'avg_holding_days': np.float64(16.5), 'total_profit': np.float64(0.17041063598152223), 'total_loss': np.float64(0.25611463232342946), 'recovery_factor': np.float64(2.7704810819345953)}}, 'GOOGL': {'train_metrics': {'total_trades': 9, 'total_return': np.float64(-0.09061170471062296), 'annual_return': np.float64(-0.024958589085084748), 'volatility': np.float64(0.10154319297180903), 'sharpe_ratio': np.float64(-0.44275335223668216), 'max_drawdown': np.float64(-0.17272582885475718), 'calmar_ratio': np.float64(-0.1444983026022824), 'win_rate': 0.4444444444444444, 'avg_win': np.float64(0.05395934401860064), 'avg_loss': np.float64(-0.038219021217443895), 'profit_factor': np.float64(1.129476209484351), 'avg_holding_days': np.float64(19.11111111111111), 'total_profit': np.float64(0.21583737607440256), 'total_loss': np.float64(0.19109510608721947), 'recovery_factor': np.float64(-0.5245984651595861)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.3888352365392251), 'avg_calmar': np.float64(0.259441555188207), 'avg_return': np.float64(0.06378127800173002)}}, '(macd_hist_positive) and (not ma_bullish)|((not cci_oversold) and (volume_above_avg)) and (rsi_rising)': {'fitness': np.float64(0.2633142157725229), 'buy_signal': '(macd_hist_positive) and (not ma_bullish)', 'sell_signal': '((not cci_oversold) and (volume_above_avg)) and (rsi_rising)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 17, 'total_return': np.float64(0.00814536064902649), 'annual_return': np.float64(0.0021610606598261306), 'volatility': np.float64(0.17753968887261862), 'sharpe_ratio': np.float64(-0.10047859976240565), 'max_drawdown': np.float64(-0.14644810187237217), 'calmar_ratio': np.float64(0.0147564948414932), 'win_rate': 0.7058823529411765, 'avg_win': np.float64(0.08186246632486131), 'avg_loss': np.float64(-0.041081619957950934), 'profit_factor': np.float64(4.7824287206970855), 'avg_holding_days': np.float64(28.235294117647058), 'total_profit': np.float64(0.9823495958983357), 'total_loss': np.float64(0.20540809978975466), 'recovery_factor': np.float64(0.05561943476826403)}}, 'MSFT': {'train_metrics': {'total_trades': 19, 'total_return': np.float64(0.9579495836856318), 'annual_return': np.float64(0.1957748013047087), 'volatility': np.float64(0.18860356907997336), 'sharpe_ratio': np.float64(0.9319802491657786), 'max_drawdown': np.float64(-0.2082728270480577), 'calmar_ratio': np.float64(0.9399920483123555), 'win_rate': 0.5263157894736842, 'avg_win': np.float64(0.06205297192087328), 'avg_loss': np.float64(-0.045115786600054636), 'profit_factor': np.float64(1.5282399305139143), 'avg_holding_days': np.float64(24.0), 'total_profit': np.float64(0.6205297192087327), 'total_loss': np.float64(0.4060420794004917), 'recovery_factor': np.float64(4.599493833463886)}}, 'GOOGL': {'train_metrics': {'total_trades': 27, 'total_return': np.float64(0.10705388283400952), 'annual_return': np.float64(0.027432882067451647), 'volatility': np.float64(0.21882998208164), 'sharpe_ratio': np.float64(0.033966470210094996), 'max_drawdown': np.float64(-0.3201002080883613), 'calmar_ratio': np.float64(0.08570091919427619), 'win_rate': 0.5925925925925926, 'avg_win': np.float64(0.056789226424744516), 'avg_loss': np.float64(-0.040857201683686076), 'profit_factor': np.float64(2.021736872798296), 'avg_holding_days': np.float64(18.333333333333332), 'total_profit': np.float64(0.9086276227959124), 'total_loss': np.float64(0.44942921852054685), 'recovery_factor': np.float64(0.33443865429933767)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.2884893732044893), 'avg_calmar': np.float64(0.34681648744937493), 'avg_return': np.float64(0.07512291467732883)}}}, 'metadata': {'timestamp': '20250818_135213', 'best_fitness': np.float64(0.8468833403246041), 'avg_fitness': np.float64(0.4574756303139469), 'population_size': 20, 'valid_individuals': 20, 'fitness_std': np.float64(0.27238547302201227), 'fitness_min': -0.0073, 'fitness_max': np.float64(0.8468833403246041)}}\n"]}], "source": ["import pickle \n", "\n", "with open(\"user_data\\\\logs\\\\20250818_135148\\\\ck_1.pkl\", \"rb\") as f:\n", "    data = pickle.load(f)\n", "    print(data)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "365ced4b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'generation': 2, 'population': [['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(high_volatility) or (ma5_gt_ma20)', '(ma5_gt_ma60) and (rsi_bullish)'], ['(rsi_falling) and (rsi_overbought)', '(macd_hist_positive) or (oversold_bounce)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)'], ['(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)']], 'halloffame': <deap.tools.support.HallOfFame object at 0x00000298FFC89A30>, 'logbook': [{'gen': 0, 'nevals': 20, 'avg': np.float64(0.1417156361497217), 'min': np.float64(-0.5192240060157292), 'max': np.float64(0.8468833403246041), 'std': np.float64(0.3245727305297713)}, {'gen': 1, 'nevals': 17, 'avg': np.float64(0.4574756303139469), 'min': np.float64(-0.0073), 'max': np.float64(0.8468833403246041), 'std': np.float64(0.27238547302201227)}, {'gen': 2, 'nevals': 16, 'avg': np.float64(0.7242858997269822), 'min': np.float64(0.6084625560052501), 'max': np.float64(0.8468833403246041), 'std': np.float64(0.07287689794512374)}], 'config': {'population_size': 20, 'generations': 10, 'max_depth': 3, 'mutation_rate': 0.2, 'crossover_rate': 0.8, 'early_stopping': True, 'patience': 10, 'min_improvement': 1e-06}, 'evaluation_results': {'(volume_above_avg) and (consecutive_up_2)|((close_gt_ma5) and (overbought_pullback)) or (kdj_bearish)': {'fitness': np.float64(-0.17535247776480403), 'buy_signal': '(volume_above_avg) and (consecutive_up_2)', 'sell_signal': '((close_gt_ma5) and (overbought_pullback)) or (kdj_bearish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 47, 'total_return': 0.0, 'annual_return': 0.0, 'volatility': np.float64(0.12206934275474418), 'sharpe_ratio': np.float64(-0.1638413015803897), 'max_drawdown': 0.0, 'calmar_ratio': 0.0, 'win_rate': 0.425531914893617, 'avg_win': np.float64(0.03196537858022761), 'avg_loss': np.float64(-0.023249139709568885), 'profit_factor': np.float64(1.018448790078481), 'avg_holding_days': np.float64(4.957446808510638), 'total_profit': np.float64(0.6393075716045521), 'total_loss': np.float64(0.6277267721583599), 'recovery_factor': 0.0}}, 'MSFT': {'train_metrics': {'total_trades': 45, 'total_return': np.float64(-0.024386417457140874), 'annual_return': np.float64(-0.0065482129697836955), 'volatility': np.float64(0.13538735861863593), 'sharpe_ratio': np.float64(-0.19609078159627646), 'max_drawdown': np.float64(-0.2045213890118611), 'calmar_ratio': np.float64(-0.032017252578916994), 'win_rate': 0.35555555555555557, 'avg_win': np.float64(0.028670253988578746), 'avg_loss': np.float64(-0.023149819006329283), 'profit_factor': np.float64(0.6832913536726861), 'avg_holding_days': np.float64(5.533333333333333), 'total_profit': np.float64(0.45872406381726), 'total_loss': np.float64(0.6713447511835492), 'recovery_factor': np.float64(-0.1192365139654249)}}, 'GOOGL': {'train_metrics': {'total_trades': 42, 'total_return': np.float64(-0.2079337981564331), 'annual_return': np.float64(-0.060146685035844416), 'volatility': np.float64(0.1537287056598114), 'sharpe_ratio': np.float64(-0.5213514593247291), 'max_drawdown': np.float64(-0.2888357664620271), 'calmar_ratio': np.float64(-0.20823835556304568), 'win_rate': 0.42857142857142855, 'avg_win': np.float64(0.04056478398077203), 'avg_loss': np.float64(-0.025905515708678353), 'profit_factor': np.float64(1.1744058032933546), 'avg_holding_days': np.float64(5.714285714285714), 'total_profit': np.float64(0.7301661116538964), 'total_loss': np.float64(0.6217323770082804), 'recovery_factor': np.float64(-0.7199032194088398)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.29376118083379843), 'avg_calmar': np.float64(-0.08008520271398756), 'avg_return': np.float64(-0.022231632668542705)}}, '(((macd_hist_negative) and (not rsi_overbought)) and (rsi_falling)) and (close_below_bb_lower)|(((weak_breakdown) and (rsi_overbought)) and (ma_bearish)) and (rsi_extreme_oversold)': {'fitness': 0.0, 'buy_signal': '(((macd_hist_negative) and (not rsi_overbought)) and (rsi_falling)) and (close_below_bb_lower)', 'sell_signal': '(((weak_breakdown) and (rsi_overbought)) and (ma_bearish)) and (rsi_extreme_oversold)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '((macd_bearish) and (kdj_overbought)) and (ma5_gt_ma10)|(cci_overbought) and (ma10_gt_ma20)': {'fitness': np.float64(-0.011983796649101782), 'buy_signal': '((macd_bearish) and (kdj_overbought)) and (ma5_gt_ma10)', 'sell_signal': '(cci_overbought) and (ma10_gt_ma20)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(0.0782207236226975), 'annual_return': np.float64(0.02024300593798456), 'volatility': np.float64(0.08053772354773775), 'sharpe_ratio': np.float64(0.003017293353722871), 'max_drawdown': np.float64(-0.2564191571117785), 'calmar_ratio': np.float64(0.07894498276179969), 'win_rate': 0.875, 'avg_win': np.float64(0.013144038985181156), 'avg_loss': np.float64(-0.028474299213547723), 'profit_factor': np.float64(3.231274357491182), 'avg_holding_days': np.float64(14.625), 'total_profit': np.float64(0.09200827289626809), 'total_loss': np.float64(0.028474299213547723), 'recovery_factor': np.float64(0.30505023300033485)}}, 'MSFT': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(0.06426425947588887), 'annual_return': np.float64(0.016712025358424976), 'volatility': np.float64(0.06359676494785432), 'sharpe_ratio': np.float64(-0.051700344259192656), 'max_drawdown': np.float64(-0.1615966909139663), 'calmar_ratio': np.float64(0.10341811619968393), 'win_rate': 0.6, 'avg_win': np.float64(0.023663313111560075), 'avg_loss': np.float64(-0.01639085778673795), 'profit_factor': np.float64(2.1655346003953215), 'avg_holding_days': np.float64(20.0), 'total_profit': np.float64(0.07098993933468023), 'total_loss': np.float64(0.0327817155734759), 'recovery_factor': np.float64(0.39768301635646125)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(0.02867125015770733), 'annual_return': np.float64(0.007550555049435026), 'volatility': np.float64(0.06084078954497826), 'sharpe_ratio': np.float64(-0.20462332990207782), 'max_drawdown': np.float64(-0.08361092296688313), 'calmar_ratio': np.float64(0.09030584499618156), 'win_rate': 0.6, 'avg_win': np.float64(0.02294745052567609), 'avg_loss': np.float64(-0.010460268781902801), 'profit_factor': np.float64(3.2906588259056826), 'avg_holding_days': np.float64(23.0), 'total_profit': np.float64(0.06884235157702827), 'total_loss': np.float64(0.020920537563805602), 'recovery_factor': np.float64(0.342912733651601)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.08443546026918254), 'avg_calmar': np.float64(0.09088964798588839), 'avg_return': np.float64(0.014835195448614854)}}, '(close_near_bb_lower) and (rsi_falling)|(cci_bearish) or (ma_bullish)': {'fitness': 0.0, 'buy_signal': '(close_near_bb_lower) and (rsi_falling)', 'sell_signal': '(cci_bearish) or (ma_bullish)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, 'strong_breakout|bb_squeeze': {'fitness': np.float64(0.25756494954584097), 'buy_signal': 'strong_breakout', 'sell_signal': 'bb_squeeze', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(0.047993548297503796), 'annual_return': np.float64(0.012552375338259703), 'volatility': np.float64(0.09018939093395958), 'sharpe_ratio': np.float64(-0.08257761344894499), 'max_drawdown': np.float64(-0.07456926700168978), 'calmar_ratio': np.float64(0.16833175171180453), 'win_rate': 0.75, 'avg_win': np.float64(0.06421323007727876), 'avg_loss': np.float64(-0.005352618646596558), 'profit_factor': np.float64(35.989802926521854), 'avg_holding_days': np.float64(19.125), 'total_profit': np.float64(0.38527938046367255), 'total_loss': np.float64(0.010705237293193117), 'recovery_factor': np.float64(0.643610299889581)}}, 'MSFT': {'train_metrics': {'total_trades': 11, 'total_return': np.float64(0.4264977341166063), 'annual_return': np.float64(0.099137622442099), 'volatility': np.float64(0.09957111326775338), 'sharpe_ratio': np.float64(0.794784951628417), 'max_drawdown': np.float64(-0.07381656621702605), 'calmar_ratio': np.float64(1.3430267421357316), 'win_rate': 0.5454545454545454, 'avg_win': np.float64(0.052607763874638856), 'avg_loss': np.float64(-0.055891021145101624), 'profit_factor': np.float64(1.1295073046826838), 'avg_holding_days': np.float64(22.0), 'total_profit': np.float64(0.3156465832478331), 'total_loss': np.float64(0.27945510572550813), 'recovery_factor': np.float64(5.777805118469912)}}, 'GOOGL': {'train_metrics': {'total_trades': 10, 'total_return': np.float64(0.013326797674263258), 'annual_return': np.float64(0.003529097361871125), 'volatility': np.float64(0.12364483610214994), 'sharpe_ratio': np.float64(-0.1332114074260355), 'max_drawdown': np.float64(-0.1569530331413693), 'calmar_ratio': np.float64(0.022485053593659633), 'win_rate': 0.5, 'avg_win': np.float64(0.06665467347577889), 'avg_loss': np.float64(-0.0572816526781029), 'profit_factor': np.float64(1.1636304184578639), 'avg_holding_days': np.float64(19.0), 'total_profit': np.float64(0.33327336737889446), 'total_loss': np.float64(0.2864082633905145), 'recovery_factor': np.float64(0.08490946245211882)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.19299864358447882), 'avg_calmar': np.float64(0.5112811824803986), 'avg_return': np.float64(0.03840636504740994)}}, 'price_falling|not ma5_gt_ma10': {'fitness': np.float64(0.3527945431546566), 'buy_signal': 'price_falling', 'sell_signal': 'not ma5_gt_ma10', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 39, 'total_return': np.float64(0.020267957129627145), 'annual_return': np.float64(0.005353725392860165), 'volatility': np.float64(0.21307215440161292), 'sharpe_ratio': np.float64(-0.06873856721574954), 'max_drawdown': np.float64(-0.21460688527097888), 'calmar_ratio': np.float64(0.024946661828208105), 'win_rate': 0.5641025641025641, 'avg_win': np.float64(0.0649381035220819), 'avg_loss': np.float64(-0.03551799688389613), 'profit_factor': np.float64(2.366055327082974), 'avg_holding_days': np.float64(20.307692307692307), 'total_profit': np.float64(1.4286382774858017), 'total_loss': np.float64(0.6038059470262344), 'recovery_factor': np.float64(0.09444225008920516)}}, 'MSFT': {'train_metrics': {'total_trades': 47, 'total_return': np.float64(1.0316274152923488), 'annual_return': np.float64(0.20758685894676687), 'volatility': np.float64(0.2167360980671075), 'sharpe_ratio': np.float64(0.8655081484796537), 'max_drawdown': np.float64(-0.2539608265958145), 'calmar_ratio': np.float64(0.8173971621109384), 'win_rate': 0.44680851063829785, 'avg_win': np.float64(0.06338237840179758), 'avg_loss': np.float64(-0.030425373126044156), 'profit_factor': np.float64(1.6825910159357522), 'avg_holding_days': np.float64(17.595744680851062), 'total_profit': np.float64(1.3310299464377493), 'total_loss': np.float64(0.7910597012771481), 'recovery_factor': np.float64(4.06215174647471)}}, 'GOOGL': {'train_metrics': {'total_trades': 53, 'total_return': np.float64(0.575850345290909), 'annual_return': np.float64(0.12865035396369895), 'volatility': np.float64(0.22098561751308712), 'sharpe_ratio': np.float64(0.49166255789141794), 'max_drawdown': np.float64(-0.4143496547597064), 'calmar_ratio': np.float64(0.3104874168129984), 'win_rate': 0.6037735849056604, 'avg_win': np.float64(0.03226189178012737), 'avg_loss': np.float64(-0.030881624549753156), 'profit_factor': np.float64(1.591916832984851), 'avg_holding_days': np.float64(14.660377358490566), 'total_profit': np.float64(1.0323805369640757), 'total_loss': np.float64(0.6485141155448163), 'recovery_factor': np.float64(1.3897690964044884)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.42947737971844074), 'avg_calmar': np.float64(0.38427708025071494), 'avg_return': np.float64(0.11386364610110866)}}, '(rsi_falling) and (rsi_overbought)|(macd_hist_positive) or (oversold_bounce)': {'fitness': np.float64(0.8543833403246041), 'buy_signal': '(rsi_falling) and (rsi_overbought)', 'sell_signal': '(macd_hist_positive) or (oversold_bounce)', 'detailed_results': {'MSFT': {'train_metrics': {'total_trades': 1, 'total_return': np.float64(0.3749659200198867), 'annual_return': np.float64(0.08842865148621959), 'volatility': np.float64(0.04500553158871368), 'sharpe_ratio': np.float64(1.5204497996281832), 'max_drawdown': np.float64(-0.3469027757991371), 'calmar_ratio': np.float64(0.2549090340442285), 'win_rate': 1.0, 'avg_win': np.float64(0.018848245906615747), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(42.0), 'total_profit': np.float64(0.018848245906615747), 'total_loss': 0, 'recovery_factor': np.float64(1.0808962803947082)}}}, 'avg_metrics': {'avg_sharpe': np.float64(1.5204497996281832), 'avg_calmar': np.float64(0.2549090340442285), 'avg_return': np.float64(0.08842865148621959)}}, '(not overbought_pullback) and (close_near_bb_lower)|((strong_breakout) and (price_gap_down)) and (volume_dry)': {'fitness': 0.0, 'buy_signal': '(not overbought_pullback) and (close_near_bb_lower)', 'sell_signal': '((strong_breakout) and (price_gap_down)) and (volume_dry)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(price_falling) and (high_volatility)|(not strong_breakout) or (rsi_extreme_oversold)': {'fitness': np.float64(-0.5108240060157292), 'buy_signal': '(price_falling) and (high_volatility)', 'sell_signal': '(not strong_breakout) or (rsi_extreme_oversold)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 2, 'total_return': np.float64(0.01880757026304858), 'annual_return': np.float64(0.004970590802615638), 'volatility': np.float64(0.016238403004110797), 'sharpe_ratio': np.float64(-0.925547247077168), 'max_drawdown': np.float64(-0.06761664250979611), 'calmar_ratio': np.float64(0.07351135191155805), 'win_rate': 1.0, 'avg_win': np.float64(0.0050739707251652305), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(4.5), 'total_profit': np.float64(0.010147941450330461), 'total_loss': 0, 'recovery_factor': np.float64(0.27815001699210057)}}, 'MSFT': {'train_metrics': {'total_trades': 1, 'total_return': np.float64(0.010133370987345458), 'annual_return': np.float64(0.0026865568308336307), 'volatility': np.float64(0.012888122691182538), 'sharpe_ratio': np.float64(-1.3433642419474663), 'max_drawdown': np.float64(-0.018764634283420123), 'calmar_ratio': np.float64(0.14317128648797559), 'win_rate': 0.0, 'avg_win': 0.0, 'avg_loss': np.float64(-0.03007828991399924), 'profit_factor': np.float64(0.0), 'avg_holding_days': np.float64(5.0), 'total_profit': 0, 'total_loss': np.float64(0.03007828991399924), 'recovery_factor': np.float64(0.540024965810232)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(-0.030021161412461028), 'annual_return': np.float64(-0.008078300333134503), 'volatility': np.float64(0.036081535467552106), 'sharpe_ratio': np.float64(-0.7781902840134156), 'max_drawdown': np.float64(-0.03282007789400243), 'calmar_ratio': np.float64(-0.2461389750269526), 'win_rate': 0.4, 'avg_win': np.float64(0.018617423888860035), 'avg_loss': np.float64(-0.024657588881700023), 'profit_factor': np.float64(0.5033588638959807), 'avg_holding_days': np.float64(3.6), 'total_profit': np.float64(0.03723484777772007), 'total_loss': np.float64(0.07397276664510007), 'recovery_factor': np.float64(-0.9147193833426922)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-1.0157005910126833), 'avg_calmar': np.float64(-0.009818778875806317), 'avg_return': np.float64(-0.00014038423322841145)}}, 'close_gt_ma10|close_gt_ma10': {'fitness': 0.0, 'buy_signal': 'close_gt_ma10', 'sell_signal': 'close_gt_ma10', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(close_gt_ma20) and (not ma5_gt_ma20)|(not cci_overbought) and (close_gt_ma20)': {'fitness': np.float64(-0.16791381234614236), 'buy_signal': '(close_gt_ma20) and (not ma5_gt_ma20)', 'sell_signal': '(not cci_overbought) and (close_gt_ma20)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 8, 'total_return': np.float64(-0.03785091995776191), 'annual_return': np.float64(-0.010215300598098498), 'volatility': np.float64(0.056817365163021126), 'sharpe_ratio': np.float64(-0.5317969341134416), 'max_drawdown': np.float64(-0.05994894982367066), 'calmar_ratio': np.float64(-0.1703999924626706), 'win_rate': 0.5, 'avg_win': np.float64(0.031899677469663164), 'avg_loss': np.float64(-0.012768870126951434), 'profit_factor': np.float64(2.49823807059734), 'avg_holding_days': np.float64(6.25), 'total_profit': np.float64(0.12759870987865266), 'total_loss': np.float64(0.051075480507805734), 'recovery_factor': np.float64(-0.6313858719642923)}}, 'MSFT': {'train_metrics': {'total_trades': 15, 'total_return': np.float64(0.07537817533746138), 'annual_return': np.float64(0.0195265740020516), 'volatility': np.float64(0.08385425649692163), 'sharpe_ratio': np.float64(-0.0056458195174121), 'max_drawdown': np.float64(-0.08678252153619925), 'calmar_ratio': np.float64(0.22500583823099168), 'win_rate': 0.3333333333333333, 'avg_win': np.float64(0.024307864858775154), 'avg_loss': np.float64(-0.01602564015789981), 'profit_factor': np.float64(0.7584054246592027), 'avg_holding_days': np.float64(7.8), 'total_profit': np.float64(0.12153932429387576), 'total_loss': np.float64(0.1602564015789981), 'recovery_factor': np.float64(0.8685870611171304)}}, 'GOOGL': {'train_metrics': {'total_trades': 11, 'total_return': np.float64(-0.041855069072135986), 'annual_return': np.float64(-0.011313101243020318), 'volatility': np.float64(0.06732948587317868), 'sharpe_ratio': np.float64(-0.46507263254618414), 'max_drawdown': np.float64(-0.1838021169960566), 'calmar_ratio': np.float64(-0.06155044037530338), 'win_rate': 0.45454545454545453, 'avg_win': np.float64(0.0590850938104716), 'avg_loss': np.float64(-0.01009618069443435), 'profit_factor': np.float64(4.876851917134947), 'avg_holding_days': np.float64(7.909090909090909), 'total_profit': np.float64(0.295425469052358), 'total_loss': np.float64(0.0605770841666061), 'recovery_factor': np.float64(-0.227718101163296)}}}, 'avg_metrics': {'avg_sharpe': np.float64(-0.3341717953923459), 'avg_calmar': np.float64(-0.0023148648689940965), 'avg_return': np.float64(-0.0006672759463557387)}}, '(close_above_bb_upper) and (price_gap_up)|(cci_bullish) and (price_rising)': {'fitness': 0.0, 'buy_signal': '(close_above_bb_upper) and (price_gap_up)', 'sell_signal': '(cci_bullish) and (price_rising)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(high_volatility) or (ma5_gt_ma20)|(ma5_gt_ma60) and (rsi_bullish)': {'fitness': np.float64(0.6149625560052501), 'buy_signal': '(high_volatility) or (ma5_gt_ma20)', 'sell_signal': '(ma5_gt_ma60) and (rsi_bullish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 13, 'total_return': np.float64(0.2487534816405501), 'annual_return': np.float64(0.06089594461610437), 'volatility': np.float64(0.24875198993938175), 'sharpe_ratio': np.float64(0.1644044923060526), 'max_drawdown': np.float64(-0.05677007468712703), 'calmar_ratio': np.float64(1.072676845181479), 'win_rate': 0.8461538461538461, 'avg_win': np.float64(0.0919353549494769), 'avg_loss': np.float64(-0.024762362334475346), 'profit_factor': np.float64(20.41987938760352), 'avg_holding_days': np.float64(81.92307692307692), 'total_profit': np.float64(1.0112889044442457), 'total_loss': np.float64(0.04952472466895069), 'recovery_factor': np.float64(4.381771259091835)}}, 'MSFT': {'train_metrics': {'total_trades': 18, 'total_return': np.float64(1.3277079774056446), 'annual_return': np.float64(0.25210558047538445), 'volatility': np.float64(0.23788402027099995), 'sharpe_ratio': np.float64(0.9757090039548153), 'max_drawdown': np.float64(-0.23937091041204842), 'calmar_ratio': np.float64(1.053200574962993), 'win_rate': 0.8333333333333334, 'avg_win': np.float64(0.057790065225418415), 'avg_loss': np.float64(-0.0500660841685446), 'profit_factor': np.float64(5.771378587435706), 'avg_holding_days': np.float64(49.666666666666664), 'total_profit': np.float64(0.8668509783812762), 'total_loss': np.float64(0.1501982525056338), 'recovery_factor': np.float64(5.546655502626255)}}, 'GOOGL': {'train_metrics': {'total_trades': 14, 'total_return': np.float64(0.926367569907905), 'annual_return': np.float64(0.19061152528506198), 'volatility': np.float64(0.266044928892038), 'sharpe_ratio': np.float64(0.641288394391072), 'max_drawdown': np.float64(-0.2651053433044748), 'calmar_ratio': np.float64(0.7190029552370949), 'win_rate': 0.8571428571428571, 'avg_win': np.float64(0.0752317827434058), 'avg_loss': np.float64(-0.11902630028902225), 'profit_factor': np.float64(3.792360977064381), 'avg_holding_days': np.float64(55.285714285714285), 'total_profit': np.float64(0.9027813929208697), 'total_loss': np.float64(0.2380526005780445), 'recovery_factor': np.float64(3.49433760316165)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.5938006302173133), 'avg_calmar': np.float64(0.9482934584605224), 'avg_return': np.float64(0.1678710167921836)}}, '(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)|((strong_breakout) and (price_rising)) and (ma20_gt_ma60)': {'fitness': np.float64(0.7039741954936695), 'buy_signal': '(((not golden_cross_buy) and (rsi_rising)) and (not macd_hist_negative)) and (not macd_bearish)', 'sell_signal': '((strong_breakout) and (price_rising)) and (ma20_gt_ma60)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 4, 'total_return': np.float64(0.7806768944190625), 'annual_return': np.float64(0.16595442385491976), 'volatility': np.float64(0.2920939669875619), 'sharpe_ratio': np.float64(0.49968311690989115), 'max_drawdown': np.float64(-0.33905140147756246), 'calmar_ratio': np.float64(0.48946685703613646), 'win_rate': 1.0, 'avg_win': np.float64(0.3510338571056776), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(292.5), 'total_profit': np.float64(1.4041354284227103), 'total_loss': 0, 'recovery_factor': np.float64(2.302532568858075)}}, 'MSFT': {'train_metrics': {'total_trades': 4, 'total_return': np.float64(2.357899566840758), 'annual_return': np.float64(0.3803475501727871), 'volatility': np.float64(0.2843912270981787), 'sharpe_ratio': np.float64(1.2670839176357096), 'max_drawdown': np.float64(-0.3091280580323244), 'calmar_ratio': np.float64(1.2303883141303709), 'win_rate': 1.0, 'avg_win': np.float64(0.1954686774658426), 'avg_loss': 0.0, 'profit_factor': 0.0, 'avg_holding_days': np.float64(285.25), 'total_profit': np.float64(0.7818747098633704), 'total_loss': 0, 'recovery_factor': np.float64(7.627581856688664)}}, 'GOOGL': {'train_metrics': {'total_trades': 5, 'total_return': np.float64(1.2687617875149382), 'annual_return': np.float64(0.24358840980522878), 'volatility': np.float64(0.3123704516597597), 'sharpe_ratio': np.float64(0.71577964118311), 'max_drawdown': np.float64(-0.3714849600694853), 'calmar_ratio': np.float64(0.6557154016670451), 'win_rate': 0.8, 'avg_win': np.float64(0.2853823357033271), 'avg_loss': np.float64(-0.1682626430826989), 'profit_factor': np.float64(6.784211408424516), 'avg_holding_days': np.float64(242.0), 'total_profit': np.float64(1.1415293428133084), 'total_loss': np.float64(0.1682626430826989), 'recovery_factor': np.float64(3.415378612575916)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.8275155585762369), 'avg_calmar': np.float64(0.7918568576111841), 'avg_return': np.float64(0.26329679461097855)}}, '(not macd_hist_negative) or (rsi_falling)|(ma5_gt_ma20) and (close_below_bb_lower)': {'fitness': 0.0, 'buy_signal': '(not macd_hist_negative) or (rsi_falling)', 'sell_signal': '(ma5_gt_ma20) and (close_below_bb_lower)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(macd_death_cross) and (macd_golden_cross)|(close_gt_ma60) or (price_gap_down)': {'fitness': 0.0, 'buy_signal': '(macd_death_cross) and (macd_golden_cross)', 'sell_signal': '(close_gt_ma60) or (price_gap_down)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(macd_positive) and (not ma5_gt_ma20)|(macd_golden_cross) and (macd_positive)': {'fitness': np.float64(0.5380866750472469), 'buy_signal': '(macd_positive) and (not ma5_gt_ma20)', 'sell_signal': '(macd_golden_cross) and (macd_positive)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 7, 'total_return': np.float64(1.0992347541580862), 'annual_return': np.float64(0.21815220718794204), 'volatility': np.float64(0.2506716897169419), 'sharpe_ratio': np.float64(0.790484986205244), 'max_drawdown': np.float64(-0.44320055525533303), 'calmar_ratio': np.float64(0.49222006741905366), 'win_rate': 0.8571428571428571, 'avg_win': np.float64(0.08266514492003213), 'avg_loss': np.float64(-0.09025219654760186), 'profit_factor': np.float64(5.495609951815317), 'avg_holding_days': np.float64(127.85714285714286), 'total_profit': np.float64(0.4959908695201928), 'total_loss': np.float64(0.09025219654760186), 'recovery_factor': np.float64(2.480219713454114)}}, 'MSFT': {'train_metrics': {'total_trades': 12, 'total_return': np.float64(0.45211054878984136), 'annual_return': np.float64(0.10435494319483452), 'volatility': np.float64(0.22869789246246242), 'sharpe_ratio': np.float64(0.36884879998918313), 'max_drawdown': np.float64(-0.2982477138495167), 'calmar_ratio': np.float64(0.3498935225618784), 'win_rate': 0.9166666666666666, 'avg_win': np.float64(0.07371721685746092), 'avg_loss': np.float64(-0.09478437739349209), 'profit_factor': np.float64(8.555095340930581), 'avg_holding_days': np.float64(60.5), 'total_profit': np.float64(0.8108893854320701), 'total_loss': np.float64(0.09478437739349209), 'recovery_factor': np.float64(1.5158894026525795)}}, 'GOOGL': {'train_metrics': {'total_trades': 12, 'total_return': np.float64(1.2270546007985756), 'annual_return': np.float64(0.2374634912506337), 'volatility': np.float64(0.26878094702699673), 'sharpe_ratio': np.float64(0.8090733128817772), 'max_drawdown': np.float64(-0.26839284036821554), 'calmar_ratio': np.float64(0.8847609009422569), 'win_rate': 0.9166666666666666, 'avg_win': np.float64(0.05522141733932915), 'avg_loss': np.float64(-0.2281054858375987), 'profit_factor': np.float64(2.662959150246341), 'avg_holding_days': np.float64(66.66666666666667), 'total_profit': np.float64(0.6074355907326207), 'total_loss': np.float64(0.2281054858375987), 'recovery_factor': np.float64(4.571860408478653)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.6561356996920681), 'avg_calmar': np.float64(0.5756248303077296), 'avg_return': np.float64(0.18665688054447008)}}, '(((cci_bearish) and (not close_gt_ma10)) and (not rsi_falling)) and (price_falling)|((overbought_pullback) or (price_falling)) or (golden_cross_buy)': {'fitness': 0.0, 'buy_signal': '(((cci_bearish) and (not close_gt_ma10)) and (not rsi_falling)) and (price_falling)', 'sell_signal': '((overbought_pullback) or (price_falling)) or (golden_cross_buy)', 'detailed_results': {}, 'avg_metrics': {'avg_sharpe': 0, 'avg_calmar': 0, 'avg_return': 0}}, '(rsi_overbought) and (ma5_gt_ma10)|(rsi_rising) and (macd_bearish)': {'fitness': np.float64(0.28500634042642065), 'buy_signal': '(rsi_overbought) and (ma5_gt_ma10)', 'sell_signal': '(rsi_rising) and (macd_bearish)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 13, 'total_return': np.float64(0.5330810376601862), 'annual_return': np.float64(0.12041656972511738), 'volatility': np.float64(0.11946789448711578), 'sharpe_ratio': np.float64(0.8405318446115828), 'max_drawdown': np.float64(-0.43628380631447056), 'calmar_ratio': np.float64(0.27600513239843216), 'win_rate': 0.5384615384615384, 'avg_win': np.float64(0.06932237925193112), 'avg_loss': np.float64(-0.019632320111046774), 'profit_factor': np.float64(4.119539039185968), 'avg_holding_days': np.float64(19.53846153846154), 'total_profit': np.float64(0.48525665476351787), 'total_loss': np.float64(0.11779392066628065), 'recovery_factor': np.float64(1.221867577812285)}}, 'MSFT': {'train_metrics': {'total_trades': 14, 'total_return': np.float64(0.4107028719418504), 'annual_return': np.float64(0.09588585336515743), 'volatility': np.float64(0.09871623075522201), 'sharpe_ratio': np.float64(0.7687272172427746), 'max_drawdown': np.float64(-0.14824243869409903), 'calmar_ratio': np.float64(0.6468178357684713), 'win_rate': 0.5, 'avg_win': np.float64(0.02434437656878889), 'avg_loss': np.float64(-0.03658780461763278), 'profit_factor': np.float64(0.6653686063759232), 'avg_holding_days': np.float64(16.5), 'total_profit': np.float64(0.17041063598152223), 'total_loss': np.float64(0.25611463232342946), 'recovery_factor': np.float64(2.7704810819345953)}}, 'GOOGL': {'train_metrics': {'total_trades': 9, 'total_return': np.float64(-0.09061170471062296), 'annual_return': np.float64(-0.024958589085084748), 'volatility': np.float64(0.10154319297180903), 'sharpe_ratio': np.float64(-0.44275335223668216), 'max_drawdown': np.float64(-0.17272582885475718), 'calmar_ratio': np.float64(-0.1444983026022824), 'win_rate': 0.4444444444444444, 'avg_win': np.float64(0.05395934401860064), 'avg_loss': np.float64(-0.038219021217443895), 'profit_factor': np.float64(1.129476209484351), 'avg_holding_days': np.float64(19.11111111111111), 'total_profit': np.float64(0.21583737607440256), 'total_loss': np.float64(0.19109510608721947), 'recovery_factor': np.float64(-0.5245984651595861)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.3888352365392251), 'avg_calmar': np.float64(0.259441555188207), 'avg_return': np.float64(0.06378127800173002)}}, '(macd_hist_positive) and (not ma_bullish)|((not cci_oversold) and (volume_above_avg)) and (rsi_rising)': {'fitness': np.float64(0.2633142157725229), 'buy_signal': '(macd_hist_positive) and (not ma_bullish)', 'sell_signal': '((not cci_oversold) and (volume_above_avg)) and (rsi_rising)', 'detailed_results': {'AAPL': {'train_metrics': {'total_trades': 17, 'total_return': np.float64(0.00814536064902649), 'annual_return': np.float64(0.0021610606598261306), 'volatility': np.float64(0.17753968887261862), 'sharpe_ratio': np.float64(-0.10047859976240565), 'max_drawdown': np.float64(-0.14644810187237217), 'calmar_ratio': np.float64(0.0147564948414932), 'win_rate': 0.7058823529411765, 'avg_win': np.float64(0.08186246632486131), 'avg_loss': np.float64(-0.041081619957950934), 'profit_factor': np.float64(4.7824287206970855), 'avg_holding_days': np.float64(28.235294117647058), 'total_profit': np.float64(0.9823495958983357), 'total_loss': np.float64(0.20540809978975466), 'recovery_factor': np.float64(0.05561943476826403)}}, 'MSFT': {'train_metrics': {'total_trades': 19, 'total_return': np.float64(0.9579495836856318), 'annual_return': np.float64(0.1957748013047087), 'volatility': np.float64(0.18860356907997336), 'sharpe_ratio': np.float64(0.9319802491657786), 'max_drawdown': np.float64(-0.2082728270480577), 'calmar_ratio': np.float64(0.9399920483123555), 'win_rate': 0.5263157894736842, 'avg_win': np.float64(0.06205297192087328), 'avg_loss': np.float64(-0.045115786600054636), 'profit_factor': np.float64(1.5282399305139143), 'avg_holding_days': np.float64(24.0), 'total_profit': np.float64(0.6205297192087327), 'total_loss': np.float64(0.4060420794004917), 'recovery_factor': np.float64(4.599493833463886)}}, 'GOOGL': {'train_metrics': {'total_trades': 27, 'total_return': np.float64(0.10705388283400952), 'annual_return': np.float64(0.027432882067451647), 'volatility': np.float64(0.21882998208164), 'sharpe_ratio': np.float64(0.033966470210094996), 'max_drawdown': np.float64(-0.3201002080883613), 'calmar_ratio': np.float64(0.08570091919427619), 'win_rate': 0.5925925925925926, 'avg_win': np.float64(0.056789226424744516), 'avg_loss': np.float64(-0.040857201683686076), 'profit_factor': np.float64(2.021736872798296), 'avg_holding_days': np.float64(18.333333333333332), 'total_profit': np.float64(0.9086276227959124), 'total_loss': np.float64(0.44942921852054685), 'recovery_factor': np.float64(0.33443865429933767)}}}, 'avg_metrics': {'avg_sharpe': np.float64(0.2884893732044893), 'avg_calmar': np.float64(0.34681648744937493), 'avg_return': np.float64(0.07512291467732883)}}}, 'metadata': {'timestamp': '20250818_135213', 'best_fitness': np.float64(0.8468833403246041), 'avg_fitness': np.float64(0.7242858997269822), 'population_size': 20, 'valid_individuals': 20, 'fitness_std': np.float64(0.07287689794512374), 'fitness_min': np.float64(0.6084625560052501), 'fitness_max': np.float64(0.8468833403246041)}}\n"]}], "source": ["with open(\"user_data\\\\logs\\\\20250818_135148\\\\ck_2.pkl\", \"rb\") as f:\n", "    data = pickle.load(f)\n", "    print(data)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}