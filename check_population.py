import pickle
import sys

# 检查种群数据
data = pickle.load(open('user_data/logs/20250818_135148/ck_2.pkl', 'rb'))
print('Population size:', len(data['population']))
print('\nSample DNA:')
for i, ind in enumerate(data['population'][:10]):
    print(f'{i+1}: {ind}')

# 检查重复DNA
dna_set = set()
duplicates = 0
for ind in data['population']:
    dna_str = str(ind)
    if dna_str in dna_set:
        duplicates += 1
    else:
        dna_set.add(dna_str)

print(f'\nTotal individuals: {len(data["population"])}')
print(f'Unique individuals: {len(dna_set)}')
print(f'Duplicates: {duplicates}')
print(f'Duplicate rate: {duplicates/len(data["population"])*100:.1f}%')
