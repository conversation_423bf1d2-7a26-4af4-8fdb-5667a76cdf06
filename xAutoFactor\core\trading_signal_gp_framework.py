# main.py - 主框架入口
"""
基于符号回归与基因编程的自动交易信号优化框架
使用DEAP库进行基因编程，支持多标的回测和信号优化
"""

import numpy as np
import pandas as pd
import warnings

warnings.filterwarnings("ignore")

from .data_manager import DataManager
from .signal_generator import SignalGenerator
from .evaluator import Evaluator
from .gp_optimizer import GeneticProgrammingOptimizer
from ..utils.reporter import Reporter
from ..utils.path_manager import get_path_manager
from loguru import logger
import os
from datetime import datetime


class TradingSignalGPFramework:
    """主框架类，协调各个模块的工作"""

    def __init__(self, config):
        """
        初始化框架

        Args:
            config (dict): 配置参数
        """
        self.config = config

        # 初始化各个模块
        self.data_manager = DataManager(config.get("data_config", {}))
        self.signal_generator = SignalGenerator(config.get("signal_config", {}))
        self.evaluator = Evaluator(config.get("eval_config", {}))
        self.gp_optimizer = GeneticProgrammingOptimizer(config.get("gp_config", {}))
        self.reporter = Reporter(config.get("report_config", {}))

        # 训练数据缓存
        self.train_data = None
        self.test_data = None
        self.symbols = config.get("symbols", [])
        self.path_manager = get_path_manager()

    def prepare_data(self):
        """准备训练和测试数据"""
        logger.info("开始准备数据...")

        # 生成或加载训练/测试数据
        train_start = self.config.get("train_start", "")
        train_end = self.config.get("train_end", "")
        test_start = self.config.get("test_start", "")
        test_end = self.config.get("test_end", "")

        self.train_data = {}
        self.test_data = {}

        for symbol in self.symbols:
            logger.info(f"加载 {symbol} 数据...")

            # 训练数据
            train_df = self.data_manager.load_data(symbol, train_start, train_end)
            # 先计算技术指标，再生成布尔指标，最终只保留OHLCV和布尔指标
            train_with_technical = self.data_manager.add_technical_indicators(train_df)
            self.train_data[symbol] = self.data_manager.add_boolean_indicators(
                train_with_technical
            )

            # 测试数据
            test_df = self.data_manager.load_data(symbol, test_start, test_end)
            test_with_technical = self.data_manager.add_technical_indicators(test_df)
            self.test_data[symbol] = self.data_manager.add_boolean_indicators(
                test_with_technical
            )

        logger.info(f"数据准备完成，共{len(self.symbols)}个标的")

    def run_optimization(self, resume_from_checkpoint=None, progress_callback=None):
        """运行遗传算法优化"""
        logger.info("开始遗传算法优化...")

        # 初始化回测结果缓存
        self.evaluation_cache = {}

        # 如果指定了检查点，则从检查点恢复
        if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
            logger.info(f"从检查点恢复: {resume_from_checkpoint}")
            checkpoint_data = self.path_manager.load_checkpoint(resume_from_checkpoint)
            population = checkpoint_data["population"]
            generation = checkpoint_data["generation"]
            halloffame = checkpoint_data["halloffame"]
            # 恢复回测结果缓存
            self.evaluation_cache = checkpoint_data.get("evaluation_results", {})
            logger.info(f"恢复了 {len(self.evaluation_cache)} 个回测结果")
        else:
            population = None
            generation = 0
            halloffame = None

        # 设置进度回调
        if progress_callback:
            self.gp_optimizer.set_progress_callback(progress_callback)

        # 定义适应度评估函数
        def evaluate_individual(individual):
            try:
                # 将个体转换为买卖信号表达式
                buy_expr, sell_expr = self.signal_generator.individual_to_expressions(
                    individual
                )

                # 创建个体的唯一标识符
                individual_key = f"{buy_expr}|{sell_expr}"

                # 检查缓存
                if individual_key in self.evaluation_cache:
                    cached_result = self.evaluation_cache[individual_key]
                    logger.debug(f"使用缓存结果: {individual_key[:50]}...")
                    return (cached_result["fitness"],)

                # 在所有训练标的上评估
                total_returns = []
                total_trades = []
                total_sharpe = []
                total_calmar = []
                total_max_dd = []
                detailed_results = {}

                # 添加调试日志
                logger.debug(f"评估个体: 买入信号={buy_expr}, 卖出信号={sell_expr}")

                for symbol in self.symbols:
                    data = self.train_data[symbol].copy()

                    # 生成交易信号
                    signals = self.signal_generator.generate_signals(
                        data, buy_expr, sell_expr
                    )

                    # 添加调试日志
                    signal_counts = signals.value_counts()
                    logger.debug(f"{symbol} 信号分布: {signal_counts.to_dict()}")

                    # 评估表现
                    metrics = self.evaluator.evaluate_signals(data, signals)

                    if metrics is not None:
                        total_returns.append(metrics.get("annual_return", 0))
                        total_trades.append(metrics.get("total_trades", 0))
                        total_sharpe.append(metrics.get("sharpe_ratio", 0))
                        total_calmar.append(metrics.get("calmar_ratio", 0))
                        total_max_dd.append(metrics.get("max_drawdown", 0))

                        # 保存详细结果 - 只保存训练指标，减少存储空间
                        detailed_results[symbol] = {
                            "train_metrics": metrics,
                        }

                        # 添加调试日志
                        logger.debug(
                            f"{symbol} 指标: 年化收益={metrics.get('annual_return', 0):.4f}, "
                            f"夏普比率={metrics.get('sharpe_ratio', 0):.4f}, "
                            f"交易次数={metrics.get('total_trades', 0)}"
                        )
                    else:
                        logger.debug(f"{symbol} 评估失败，返回None")

                if not total_returns:
                    logger.debug("所有标的评估都失败，返回0适应度")
                    fitness = 0.0
                else:
                    # 计算平均指标作为适应度
                    avg_sharpe = np.mean([s for s in total_sharpe if not np.isnan(s)])
                    avg_calmar = np.mean([c for c in total_calmar if not np.isnan(c)])
                    avg_return = np.mean(total_returns)

                    # 综合适应度：夏普比率权重0.5，卡玛比率权重0.3，年化收益权重0.2
                    fitness = 0.5 * avg_sharpe + 0.3 * avg_calmar + 0.2 * avg_return

                    # 添加调试日志
                    logger.debug(
                        f"最终适应度: {fitness:.6f} "
                        f"(夏普={avg_sharpe:.4f}, 卡玛={avg_calmar:.4f}, 收益={avg_return:.4f})"
                    )

                # 缓存结果
                self.evaluation_cache[individual_key] = {
                    "fitness": fitness,
                    "buy_signal": buy_expr,
                    "sell_signal": sell_expr,
                    "detailed_results": detailed_results,
                    "avg_metrics": {
                        "avg_sharpe": (
                            np.mean([s for s in total_sharpe if not np.isnan(s)])
                            if total_sharpe
                            else 0
                        ),
                        "avg_calmar": (
                            np.mean([c for c in total_calmar if not np.isnan(c)])
                            if total_calmar
                            else 0
                        ),
                        "avg_return": np.mean(total_returns) if total_returns else 0,
                    },
                }

                return (fitness,)

            except Exception as e:
                logger.error(f"评估个体时出错: {e}")
                return (0.0,)

        # 运行遗传算法
        best_individuals, stats = self.gp_optimizer.evolve(
            evaluate_individual,
            population=population,
            start_generation=generation,
            halloffame=halloffame,
            progress_callback=progress_callback,
            evaluation_cache=self.evaluation_cache,
        )

        logger.info(
            f"遗传算法优化完成, 共找到 {len(best_individuals)} 个最优个体, 统计信息: {stats}"
        )
        return best_individuals, stats

    def generate_final_report(self, best_individuals):
        """生成最终训练报告"""
        logger.info("生成最终报告...")

        # 从缓存中获取所有评估过的个体
        all_cached_results = []
        for individual_key, cached_data in self.evaluation_cache.items():
            if cached_data["fitness"] > 0:  # 只包含有效的策略
                all_cached_results.append(cached_data)

        # 按适应度排序
        all_cached_results.sort(key=lambda x: x["fitness"], reverse=True)

        logger.info(f"从缓存中找到 {len(all_cached_results)} 个有效策略")

        detailed_results = []

        # 处理所有缓存的结果
        for i, cached_data in enumerate(all_cached_results):
            try:
                buy_expr = cached_data["buy_signal"]
                sell_expr = cached_data["sell_signal"]

                # 在测试集上评估
                test_metrics_list = []
                train_metrics_list = []

                for symbol in self.symbols:
                    # 从缓存获取训练集结果
                    if symbol in cached_data["detailed_results"]:
                        train_metrics = cached_data["detailed_results"][symbol][
                            "train_metrics"
                        ]
                        if train_metrics:
                            train_metrics_list.append(train_metrics)

                    # 测试集表现 - 需要重新计算
                    test_data = self.test_data[symbol].copy()
                    test_signals = self.signal_generator.generate_signals(
                        test_data, buy_expr, sell_expr
                    )
                    test_metrics = self.evaluator.evaluate_signals(
                        test_data, test_signals
                    )
                    if test_metrics:
                        test_metrics_list.append(test_metrics)

                # 计算平均指标
                if test_metrics_list and train_metrics_list:
                    result = {
                        "rank": len(detailed_results) + 1,  # 使用实际排名
                        "buy_signal": buy_expr,
                        "sell_signal": sell_expr,
                        "train_metrics": self._average_metrics(train_metrics_list),
                        "test_metrics": self._average_metrics(test_metrics_list),
                        "fitness": cached_data["fitness"],
                    }
                    detailed_results.append(result)

            except Exception as e:
                logger.error(f"处理第{i+1}个缓存策略时出错: {e}")
                continue

        logger.info(f"成功处理了 {len(detailed_results)} 个有效策略")

        # 生成HTML报告 - 只显示前10个
        top_results = detailed_results[:10]
        report = self.reporter.generate_report(top_results)

        # 保存HTML报告到会话目录
        self.reporter.save_report(report)

        # 生成并保存CSV报告 - 包含所有策略
        self._save_csv_report(detailed_results)

        # 获取报告路径用于返回
        report_path = str(self.path_manager.get_report_path())

        return report_path, detailed_results

    def _save_csv_report(self, detailed_results):
        """保存CSV格式的详细报告"""
        try:
            import pandas as pd

            # 准备CSV数据
            csv_data = []
            for result in detailed_results:
                train_metrics = result["train_metrics"]
                test_metrics = result["test_metrics"]

                row = {
                    "排名": result["rank"],
                    "买入信号": result["buy_signal"],
                    "卖出信号": result["sell_signal"],
                    # 测试集指标
                    "测试_年化收益率": test_metrics.get("annual_return", 0),
                    "测试_夏普比率": test_metrics.get("sharpe_ratio", 0),
                    "测试_卡玛比率": test_metrics.get("calmar_ratio", 0),
                    "测试_最大回撤": test_metrics.get("max_drawdown", 0),
                    "测试_交易次数": test_metrics.get("total_trades", 0),
                    "测试_胜率": test_metrics.get("win_rate", 0),
                    "测试_平均持仓天数": test_metrics.get("avg_holding_days", 0),
                    # 训练集指标
                    "训练_年化收益率": train_metrics.get("annual_return", 0),
                    "训练_夏普比率": train_metrics.get("sharpe_ratio", 0),
                    "训练_卡玛比率": train_metrics.get("calmar_ratio", 0),
                    "训练_最大回撤": train_metrics.get("max_drawdown", 0),
                    "训练_交易次数": train_metrics.get("total_trades", 0),
                    "训练_胜率": train_metrics.get("win_rate", 0),
                    "训练_平均持仓天数": train_metrics.get("avg_holding_days", 0),
                }
                csv_data.append(row)

            # 创建DataFrame并保存
            df = pd.DataFrame(csv_data)
            csv_path = self.path_manager.get_session_dir() / "strategies_report.csv"
            df.to_csv(csv_path, index=False, encoding="utf-8-sig")

            logger.info(f"CSV报告已保存: {csv_path}")

        except Exception as e:
            logger.error(f"保存CSV报告失败: {e}")

    def _average_metrics(self, metrics_list):
        """计算平均指标"""
        if not metrics_list:
            return {}

        avg_metrics = {}
        for key in metrics_list[0].keys():
            values = [m[key] for m in metrics_list if key in m and not np.isnan(m[key])]
            if values:
                avg_metrics[key] = np.mean(values)
            else:
                avg_metrics[key] = 0.0

        return avg_metrics

    def test_single_strategy(self, buy_expr, sell_expr, symbol=None):
        """测试单个策略表达式"""
        logger.info(f"测试策略: Buy={buy_expr}, Sell={sell_expr}")

        if symbol:
            symbols_to_test = [symbol]
        else:
            symbols_to_test = self.symbols

        results = {}

        for sym in symbols_to_test:
            # 合并训练和测试数据进行完整回测
            train_data = self.train_data[sym].copy()
            test_data = self.test_data[sym].copy()
            full_data = pd.concat([train_data, test_data], ignore_index=True)

            # 确保索引是datetime类型
            if not isinstance(full_data.index, pd.DatetimeIndex):
                # 如果索引不是datetime，尝试转换
                try:
                    if isinstance(full_data.index, pd.RangeIndex):
                        # 如果是RangeIndex，使用日期列作为索引
                        if "Date" in full_data.columns:
                            full_data.set_index("Date", inplace=True)
                        else:
                            # 创建日期索引
                            start_date = pd.Timestamp("2020-01-01")
                            date_range = pd.date_range(
                                start=start_date, periods=len(full_data), freq="D"
                            )
                            full_data.index = date_range
                    else:
                        # 尝试将现有索引转换为datetime
                        full_data.index = pd.to_datetime(full_data.index)
                except Exception as e:
                    logger.warning(f"无法转换索引为datetime: {e}")
                    # 创建默认的日期索引
                    start_date = pd.Timestamp("2020-01-01")
                    date_range = pd.date_range(
                        start=start_date, periods=len(full_data), freq="D"
                    )
                    full_data.index = date_range

            # 调试信息
            logger.info(
                f"数据形状: {full_data.shape}, 索引类型: {type(full_data.index)}"
            )
            logger.info(f"数据列: {list(full_data.columns)}")
            if "RSI" in full_data.columns:
                logger.info(
                    f"RSI数据范围: {full_data['RSI'].min():.2f} - {full_data['RSI'].max():.2f}"
                )

            # 生成信号
            signals = self.signal_generator.generate_signals(
                full_data, buy_expr, sell_expr
            )

            # 调试信号信息
            logger.info(f"信号形状: {signals.shape}, 信号类型: {signals.dtype}")
            logger.info(
                f"信号值统计: 买入={sum(signals == 1)}, 卖出={sum(signals == -1)}, 持有={sum(signals == 0)}"
            )

            # 确保信号和数据有相同的索引
            if not signals.index.equals(full_data.index):
                logger.warning(f"信号和数据索引不匹配，重新对齐索引")
                signals = signals.reindex(full_data.index, fill_value=0)

            # 详细评估
            metrics = self.evaluator.evaluate_signals(full_data, signals)
            trade_records = self.evaluator.get_trade_records()

            results[sym] = {
                "metrics": metrics,
                "trade_records": trade_records,
                "signals": signals,
            }

        return results

    def print_backtest_result(self, results):
        # 打印结果
        print("\n" + "=" * 50)
        print("策略测试结果:")
        print("=" * 50)

        for symbol, result in results.items():
            print(f"\n股票: {symbol}")
            metrics = result["metrics"]

            if metrics is None:
                print("策略评估失败，无法生成指标")
                continue

            print(f"总收益率: {metrics.get('total_return', 0):.2%}")
            print(f"年化收益率: {metrics.get('annual_return', 0):.2%}")
            print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
            print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"胜率: {metrics.get('win_rate', 0):.2%}")
            print(f"交易次数: {metrics.get('trade_count', 0)}")

            # 显示交易记录
            trade_records = result["trade_records"]
            if trade_records:
                print(f"\n交易记录 (前5笔):")
                for i, trade in enumerate(trade_records[:5]):
                    print(
                        f"  {i+1}. 买入: {trade['entry_price']:.2f} ({trade['entry_date'].strftime('%Y-%m-%d')}) -> 卖出: {trade['exit_price']:.2f} ({trade['exit_date'].strftime('%Y-%m-%d')}) - 收益: {trade['net_return']:.2%}"
                    )
                if len(trade_records) > 5:
                    print(f"  ... 还有 {len(trade_records)-5} 笔交易")
