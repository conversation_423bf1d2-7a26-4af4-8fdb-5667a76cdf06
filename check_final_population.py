import pickle
import sys

# 检查最新的种群数据
data = pickle.load(open('user_data/logs/20250818_162854/ck_16.pkl', 'rb'))
print('Population size:', len(data['population']))
print('\nSample DNA:')
for i, ind in enumerate(data['population'][:10]):
    if len(ind) >= 2:
        print(f'{i+1}: 买入={ind[0]}, 卖出={ind[1]}')
    else:
        print(f'{i+1}: {ind}')

# 检查重复DNA
dna_set = set()
duplicates = 0
for ind in data['population']:
    if len(ind) >= 2:
        dna_str = f"{ind[0]}|{ind[1]}"
    else:
        dna_str = str(ind)
    
    if dna_str in dna_set:
        duplicates += 1
    else:
        dna_set.add(dna_str)

print(f'\nTotal individuals: {len(data["population"])}')
print(f'Unique individuals: {len(dna_set)}')
print(f'Duplicates: {duplicates}')
print(f'Duplicate rate: {duplicates/len(data["population"])*100:.1f}%')

# 分析DNA复杂度
print(f'\nDNA复杂度分析:')
complexity_stats = {}
for ind in data['population']:
    if len(ind) >= 2:
        buy_signal = ind[0]
        sell_signal = ind[1]
        
        # 计算买入信号复杂度（and/or操作符数量）
        buy_complexity = buy_signal.count(' and ') + buy_signal.count(' or ') + 1
        sell_complexity = sell_signal.count(' and ') + sell_signal.count(' or ') + 1
        
        total_complexity = buy_complexity + sell_complexity
        
        if total_complexity not in complexity_stats:
            complexity_stats[total_complexity] = 0
        complexity_stats[total_complexity] += 1

print("复杂度分布:")
for complexity in sorted(complexity_stats.keys()):
    count = complexity_stats[complexity]
    percentage = count / len(data['population']) * 100
    print(f"  复杂度 {complexity}: {count} 个 ({percentage:.1f}%)")

# 显示Hall of Fame中的多样性
print(f'\nHall of Fame 多样性:')
if 'halloffame' in data:
    hof_set = set()
    for i, ind in enumerate(data['halloffame']):
        if len(ind) >= 2:
            ind_str = f"{ind[0]}|{ind[1]}"
            if ind_str not in hof_set:
                hof_set.add(ind_str)
                print(f"  {i+1}: 买入={ind[0]}, 卖出={ind[1]}")
    print(f"Hall of Fame 中有 {len(hof_set)} 个不同的策略")

# 对比之前的结果
print(f'\n与之前结果对比:')
print(f'之前(ck_2.pkl): 20个个体, 85%重复率, 复杂DNA')
print(f'现在(ck_16.pkl): {len(data["population"])}个个体, {duplicates/len(data["population"])*100:.1f}%重复率')

# 检查信号池使用情况
print(f'\n信号使用分析:')
buy_signals_used = set()
sell_signals_used = set()

for ind in data['population']:
    if len(ind) >= 2:
        # 提取基础信号（去除not前缀和and/or组合）
        buy_signal = ind[0].replace('not ', '').split(' and ')[0].split(' or ')[0].strip()
        sell_signal = ind[1].replace('not ', '').split(' and ')[0].split(' or ')[0].strip()
        
        buy_signals_used.add(buy_signal)
        sell_signals_used.add(sell_signal)

print(f'使用的买入信号种类: {len(buy_signals_used)}')
print(f'使用的卖出信号种类: {len(sell_signals_used)}')
print(f'买入信号: {sorted(list(buy_signals_used))[:10]}...')
print(f'卖出信号: {sorted(list(sell_signals_used))[:10]}...')
