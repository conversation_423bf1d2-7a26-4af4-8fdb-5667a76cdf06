#!/usr/bin/env python3
"""
独立训练监控系统
基于checkpoints目录监控训练进度，不依赖训练进程
"""

import os
import json
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from loguru import logger
from ..training.training_process_manager import TrainingProcessManager, get_training_managers


class IndependentMonitor:
    """独立监控器"""
    
    def __init__(self):
        # 使用路径管理器
        from ..utils.path_manager import get_path_manager
        self.path_manager = get_path_manager()
        self.monitoring_interval = 2  # 监控间隔（秒）
    
    def get_all_training_status(self) -> List[Dict[str, Any]]:
        """获取所有训练状态"""
        managers = get_training_managers()
        all_status = []
        
        for manager in managers:
            status = manager.get_status()
            checkpoints = manager.get_checkpoints()
            
            # 添加checkpoint信息
            status["checkpoints"] = checkpoints
            status["checkpoint_count"] = len(checkpoints)
            
            # 计算进度百分比
            if status.get("total_generations", 0) > 0:
                status["progress_percentage"] = (
                    status.get("current_generation", 0) / status["total_generations"]
                ) * 100
            else:
                status["progress_percentage"] = 0
            
            # 格式化时间
            if status.get("elapsed_time"):
                status["elapsed_time_formatted"] = self._format_time(status["elapsed_time"])
            if status.get("estimated_remaining"):
                status["estimated_remaining_formatted"] = self._format_time(status["estimated_remaining"])
            
            all_status.append(status)
        
        return all_status
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.0f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
    
    def start_training(self, config_file: str = "") -> Dict[str, Any]:
        """启动训练"""
        try:
            # 创建新的训练管理器
            config = {}
            manager = TrainingProcessManager(config)
            
            # 启动训练
            success = manager.start_training(config_file)
            
            if success:
                return {
                    "success": True,
                    "training_id": manager.training_id,
                    "process_id": manager.process_id
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to start training"
                }
                
        except Exception as e:
            logger.error(f"Error starting training: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def stop_training(self, training_id: str) -> Dict[str, Any]:
        """停止训练"""
        try:
            managers = get_training_managers()
            
            for manager in managers:
                if manager.training_id == training_id:
                    success = manager.stop_training()
                    return {
                        "success": success,
                        "message": "Training stopped" if success else "Failed to stop training"
                    }
            
            return {
                "success": False,
                "error": "Training not found"
            }
            
        except Exception as e:
            logger.error(f"Error stopping training: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_training_details(self, training_id: str) -> Dict[str, Any]:
        """获取训练详细信息"""
        try:
            managers = get_training_managers()
            
            for manager in managers:
                if manager.training_id == training_id:
                    status = manager.get_status()
                    checkpoints = manager.get_checkpoints()
                    
                    return {
                        "success": True,
                        "status": status,
                        "checkpoints": checkpoints
                    }
            
            return {
                "success": False,
                "error": "Training not found"
            }
            
        except Exception as e:
            logger.error(f"Error getting training details: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import psutil
            
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_gb": memory.used / (1024**3),
                "memory_total_gb": memory.total / (1024**3),
                "disk_percent": disk.percent,
                "disk_used_gb": disk.used / (1024**3),
                "disk_total_gb": disk.total / (1024**3),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {}
    
    def cleanup_old_checkpoints(self, training_id: str, keep_count: int = 5) -> Dict[str, Any]:
        """清理旧的检查点"""
        try:
            managers = get_training_managers()
            
            for manager in managers:
                if manager.training_id == training_id:
                    # 这里需要实现清理逻辑
                    # 暂时返回成功
                    return {
                        "success": True,
                        "message": f"Kept {keep_count} latest checkpoints"
                    }
            
            return {
                "success": False,
                "error": "Training not found"
            }
            
        except Exception as e:
            logger.error(f"Error cleaning checkpoints: {e}")
            return {
                "success": False,
                "error": str(e)
            }
