#!/usr/bin/env python3
"""
检查点管理模块
提供检查点管理和恢复训练功能
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from loguru import logger
from ..utils.path_manager import get_path_manager
from ..core.trading_signal_gp_framework import TradingSignalGPFramework


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, session_id: str = None):
        # 使用路径管理器获取session目录
        self.path_manager = get_path_manager()
        
        if session_id:
            # 如果指定了session_id，使用指定的session目录
            self.session_dir = self.path_manager.get_user_data_root() / "logs" / session_id
        else:
            # 使用当前session目录
            self.session_dir = self.path_manager.get_session_dir()
        
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """列出当前session的检查点"""
        try:
            checkpoints = self.path_manager.list_checkpoints()
            # 添加session_id信息以保持兼容性
            for checkpoint in checkpoints:
                checkpoint["session_id"] = self.session_dir.name
            return checkpoints
        except Exception as e:
            logger.error(f"Error listing checkpoints: {e}")
            return []
    
    def get_checkpoints(self) -> List[Dict[str, Any]]:
        """获取当前session的检查点"""
        return self.list_checkpoints()
    

    
    def save_checkpoint(self, generation: int, data: Any) -> str:
        """保存检查点到当前session目录"""
        try:
            checkpoint_path = self.path_manager.save_checkpoint(generation, data)
            return str(checkpoint_path)
        except Exception as e:
            logger.error(f"Error saving checkpoint: {e}")
            raise
    
    def save_session_info(self, info: Dict[str, Any]) -> str:
        """保存session信息"""
        info_file = self.session_dir / "session_info.json"
        
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Session info saved: {info_file}")
            return str(info_file)
        except Exception as e:
            logger.error(f"Error saving session info: {e}")
            raise
    
    def load_session_info(self) -> Optional[Dict[str, Any]]:
        """加载当前session信息"""
        info_file = self.session_dir / "session_info.json"
        
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                return info
            except Exception as e:
                logger.error(f"Error loading session info: {e}")
        
        return None
    
    def resume_training(self, checkpoint_path: str, config: Dict[str, Any]) -> Any:
        """从检查点恢复训练"""
        logger.info(f"Resuming training from checkpoint: {checkpoint_path}")
        
        # 加载检查点数据
        checkpoint_data = self.path_manager.load_checkpoint(checkpoint_path)
        
        # 创建框架实例
        framework = TradingSignalGPFramework(config)
        
        # 准备数据
        framework.prepare_data()
        
        # 恢复训练
        best_individuals, stats = framework.run_optimization(checkpoint_data)
        
        return best_individuals, stats
    
    def cleanup_old_checkpoints(self, keep_count: int = 5) -> int:
        """清理旧的检查点，只保留最新的几个"""
        try:
            return self.path_manager.cleanup_old_checkpoints(keep_count)
        except Exception as e:
            logger.error(f"Error cleaning up checkpoints: {e}")
            return 0


def list_checkpoints():
    """列出可用的检查点文件"""
    manager = CheckpointManager()
    checkpoints = manager.list_checkpoints()
    
    if not checkpoints:
        print("没有找到检查点文件")
        return []
    
    print("可用的检查点文件:")
    for i, checkpoint in enumerate(checkpoints, 1):
        print(f"{i}. {checkpoint['filename']} "
              f"(会话: {checkpoint['session_id']}, "
              f"代: {checkpoint['generation']}, "
              f"时间: {checkpoint['datetime']}, "
              f"大小: {checkpoint['size_kb']} KB)")
    
    return checkpoints


def resume_training():
    """恢复训练"""
    print("恢复训练模式")
    
    # 列出检查点
    checkpoints = list_checkpoints()
    if not checkpoints:
        return
    
    choice = input("请选择要恢复的检查点编号: ")
    
    try:
        idx = int(choice) - 1
        if 0 <= idx < len(checkpoints):
            checkpoint_path = checkpoints[idx]['path']
            
            # 加载配置
            from ..utils.environment_setup import EnvironmentSetup
            env_setup = EnvironmentSetup()
            config = env_setup.load_config("config.json")
            
            # 恢复训练
            manager = CheckpointManager()
            best_individuals, stats = manager.resume_training(checkpoint_path, config)
            
            print("训练恢复完成！")
            return best_individuals, stats
        else:
            print("无效的选择")
    except ValueError:
        print("请输入有效的数字")
    except Exception as e:
        print(f"恢复训练失败: {e}")


def resume_from_checkpoint(checkpoint_file: str, config_file: str = "config.json"):
    """从指定检查点恢复训练"""
    print(f"从检查点恢复训练: {checkpoint_file}")
    
    # 检查检查点文件是否存在
    if not os.path.exists(checkpoint_file):
        print(f"检查点文件不存在: {checkpoint_file}")
        return
    
    try:
        # 加载配置
        from ..utils.environment_setup import EnvironmentSetup
        env_setup = EnvironmentSetup()
        config = env_setup.load_config(config_file)
        
        # 恢复训练
        manager = CheckpointManager()
        best_individuals, stats = manager.resume_training(checkpoint_file, config)
        
        print("训练恢复完成！")
        return best_individuals, stats
    except Exception as e:
        print(f"恢复训练失败: {e}")
        raise
