"""
xAutoFactor 核心模块
包含数据处理、评估、信号生成和遗传编程优化等核心功能
"""

from .data_manager import DataManager
from .evaluator import Evaluator
from .signal_generator import SignalGenerator
from .gp_optimizer import GeneticProgrammingOptimizer
from .trading_signal_gp_framework import TradingSignalGPFramework
from .strategy_tester import StrategyTester

__all__ = [
    "DataManager",
    "Evaluator",
    "SignalGenerator", 
    "GeneticProgrammingOptimizer",
    "TradingSignalGPFramework",
    "StrategyTester",
]
