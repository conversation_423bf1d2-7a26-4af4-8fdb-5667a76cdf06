"""
测试信号评估功能
"""

import json
from pathlib import Path
from xAutoFactor.core.signal_evaluation_workflow import SignalEvaluationWorkflow
from xAutoFactor.utils.path_manager import get_path_manager


def test_signal_evaluation():
    """测试信号评估功能"""

    # 加载配置
    config_path = Path("user_data/config/quick.json")
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)

    print("配置加载完成")
    print(f"股票列表: {config['symbols']}")

    # 创建测试会话
    path_manager = get_path_manager()
    try:
        path_manager.create_training_session()
        print(f"创建测试会话: {path_manager.get_current_session_id()}")
    except ValueError:
        # 如果会话已存在，使用现有会话
        print(f"使用现有会话: {path_manager.get_current_session_id()}")

    # 初始化信号评估工作流
    workflow = SignalEvaluationWorkflow(config)

    # 强制重新评估
    workflow.force_reevaluate = True

    # 运行信号评估
    print("\n开始信号评估...")
    buy_signals, sell_signals = workflow.prepare_signal_pools_for_training(
        config["symbols"][:1]  # 只测试第一个股票
    )

    print(f"\n评估结果:")
    print(f"有效买入信号数量: {len(buy_signals)}")
    print(f"有效卖出信号数量: {len(sell_signals)}")

    if buy_signals:
        print(f"\n前10个买入信号:")
        for i, signal in enumerate(buy_signals[:10], 1):
            print(f"  {i:2d}. {signal}")

    if sell_signals:
        print(f"\n前10个卖出信号:")
        for i, signal in enumerate(sell_signals[:10], 1):
            print(f"  {i:2d}. {signal}")

    # 显示评估摘要
    summary = workflow.get_evaluation_summary()
    print(f"\n评估摘要:")
    for key, value in summary.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    test_signal_evaluation()
