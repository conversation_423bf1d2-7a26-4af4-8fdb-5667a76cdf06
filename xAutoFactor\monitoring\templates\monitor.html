
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoFactor 训练监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
        }
        
        .status-card h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .status-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-card .unit {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .progress-section {
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container h3 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info { color: #3498db; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.success { color: #2ecc71; }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .running-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
            margin-right: 8px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .stopped-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AutoFactor 训练监控</h1>
            <p>实时监控遗传算法训练进度和结果</p>
        </div>
        
        <div class="content">
            <!-- 状态卡片 -->
            <div class="status-grid">
                <div class="status-card">
                    <h3>训练状态</h3>
                    <div class="value" id="training-status">
                        <span class="stopped-indicator"></span>已停止
                    </div>
                </div>
                
                <div class="status-card">
                    <h3>当前阶段</h3>
                    <div class="value" id="current-phase">准备中</div>
                </div>
                
                <div class="status-card">
                    <h3>当前代数</h3>
                    <div class="value" id="current-generation">0</div>
                    <div class="unit">/ <span id="total-generations">0</span></div>
                </div>
                
                <div class="status-card">
                    <h3>最优适应度</h3>
                    <div class="value" id="best-fitness">0.0000</div>
                </div>
                
                <div class="status-card">
                    <h3>平均适应度</h3>
                    <div class="value" id="avg-fitness">0.0000</div>
                </div>
                
                <div class="status-card">
                    <h3>种群大小</h3>
                    <div class="value" id="population-size">0</div>
                </div>
                
                <div class="status-card">
                    <h3>已用时间</h3>
                    <div class="value" id="elapsed-time">00:00:00</div>
                </div>
                
                <div class="status-card">
                    <h3>预计剩余</h3>
                    <div class="value" id="estimated-remaining">00:00:00</div>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-section">
                <h3>训练进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div style="text-align: center; margin-top: 10px;">
                    <span id="progress-text">0%</span>
                </div>
            </div>
            
            <!-- 适应度历史图表 -->
            <div class="chart-container">
                <h3>适应度历史</h3>
                <canvas id="fitness-chart" width="800" height="200"></canvas>
            </div>
            
            <!-- 日志 -->
            <div class="chart-container">
                <h3>训练日志</h3>
                <div class="log-container" id="log-container">
                    <div class="log-entry info">等待训练开始...</div>
                </div>
            </div>
            
            <!-- 控制按钮 -->
            <div class="controls">
                <button class="btn btn-primary" onclick="startTraining()">开始训练</button>
                <button class="btn btn-danger" onclick="stopTraining()">停止训练</button>
                <button class="btn btn-success" onclick="refreshData()">刷新数据</button>
            </div>
        </div>
    </div>
    
    <script>
        let chart = null;
        
        // 初始化图表
        function initChart() {
            const canvas = document.getElementById('fitness-chart');
            const ctx = canvas.getContext('2d');
            
            // 简单的折线图绘制
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.fillStyle = 'rgba(0, 123, 255, 0.1)';
        }
        
        // 更新图表
        function updateChart(fitnessHistory, generationHistory) {
            const canvas = document.getElementById('fitness-chart');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            
            // 清除画布
            ctx.clearRect(0, 0, width, height);
            
            if (fitnessHistory.length < 2) return;
            
            // 计算缩放
            const maxFitness = Math.max(...fitnessHistory);
            const minFitness = Math.min(...fitnessHistory);
            const range = maxFitness - minFitness || 1;
            
            // 绘制折线
            ctx.beginPath();
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            
            for (let i = 0; i < fitnessHistory.length; i++) {
                const x = (i / (fitnessHistory.length - 1)) * width;
                const y = height - ((fitnessHistory[i] - minFitness) / range) * height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            // 绘制填充区域
            ctx.beginPath();
            ctx.fillStyle = 'rgba(0, 123, 255, 0.1)';
            
            for (let i = 0; i < fitnessHistory.length; i++) {
                const x = (i / (fitnessHistory.length - 1)) * width;
                const y = height - ((fitnessHistory[i] - minFitness) / range) * height;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.lineTo(width, height);
            ctx.lineTo(0, height);
            ctx.closePath();
            ctx.fill();
        }
        
        // 更新页面数据
        function updatePage(data) {
            // 更新状态
            document.getElementById('training-status').innerHTML = 
                data.is_running ? 
                '<span class="running-indicator"></span>运行中' : 
                '<span class="stopped-indicator"></span>已停止';
            
            document.getElementById('current-phase').textContent = data.current_phase;
            document.getElementById('current-generation').textContent = data.current_generation;
            document.getElementById('total-generations').textContent = data.total_generations;
            document.getElementById('best-fitness').textContent = data.best_fitness.toFixed(4);
            document.getElementById('avg-fitness').textContent = data.avg_fitness.toFixed(4);
            document.getElementById('population-size').textContent = data.population_size;
            document.getElementById('elapsed-time').textContent = data.elapsed_time;
            document.getElementById('estimated-remaining').textContent = data.estimated_remaining;
            
            // 更新进度条
            const progressPercent = data.progress_percentage;
            document.getElementById('progress-fill').style.width = progressPercent + '%';
            document.getElementById('progress-text').textContent = progressPercent.toFixed(1) + '%';
            
            // 更新图表
            updateChart(data.fitness_history, data.generation_history);
            
            // 更新日志
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '';
            data.log_messages.slice(-20).forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${log.level}`;
                logEntry.textContent = `[${log.timestamp}] ${log.message}`;
                logContainer.appendChild(logEntry);
            });
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 获取数据
        function refreshData() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    updatePage(data);
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                });
        }
        
        // 开始训练
        function startTraining() {
            fetch('/api/start', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('训练已开始');
                    } else {
                        alert('启动训练失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('启动训练失败:', error);
                });
        }
        
        // 停止训练
        function stopTraining() {
            fetch('/api/stop', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('训练已停止');
                    } else {
                        alert('停止训练失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('停止训练失败:', error);
                });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            refreshData();
            
            // 定期刷新数据
            setInterval(refreshData, 1000);
        });
    </script>
</body>
</html>
    