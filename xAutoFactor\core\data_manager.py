# data_manager.py - 数据管理模块
"""
负责数据获取、预处理和技术指标计算
使用yfinance获取数据，talib计算技术指标
新增：预定义布尔指标生成功能
"""

import pandas as pd
import numpy as np
import yfinance as yf
import talib
import os
import time
from typing import Dict, List
from loguru import logger
from ..utils.path_manager import get_path_manager


class DataManager:
    """数据管理器，负责数据获取和预处理"""

    def __init__(self, config: Dict):
        """
        初始化数据管理器

        Args:
            config (dict): 数据配置参数
        """
        self.config = config
        self.use_cache = config.get("use_cache", True)

        # 使用路径管理器获取缓存目录
        path_manager = get_path_manager()
        self.cache_dir = path_manager.get_user_data_root() / "data_cache"
        self.cache_dir.mkdir(exist_ok=True)

    def load_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        加载指定股票的历史数据

        Args:
            symbol (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期

        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        cache_file = self.cache_dir / f"{symbol}_{start_date}_{end_date}.csv"

        # 尝试从缓存加载
        if self.use_cache and os.path.exists(cache_file):
            try:
                logger.info(f"从缓存加载 {symbol} 数据")
                df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                return df
            except Exception as e:
                logger.warning(f"缓存加载失败: {e}")

        # 从yfinance获取数据
        try:
            logger.info(f"从Yahoo Finance下载 {symbol} 数据")
            ticker = yf.Ticker(symbol)
            df = ticker.history(start=start_date, end=end_date)
            time.sleep(3)

            if df.empty:
                raise ValueError(f"无法获取 {symbol} 的数据")

            # 检查并重命名列名为标准格式
            if len(df.columns) == 7:
                # yfinance返回的列: Open, High, Low, Close, Volume, Dividends, Stock Splits
                df.columns = [
                    "Open",
                    "High",
                    "Low",
                    "Close",
                    "Volume",
                    "Dividends",
                    "Stock Splits",
                ]
                # 只保留需要的列
                df = df[["Open", "High", "Low", "Close", "Volume"]]
            elif len(df.columns) == 5:
                # 已经是标准格式
                df.columns = ["Open", "High", "Low", "Close", "Volume"]
            else:
                # 其他情况，尝试按顺序重命名
                expected_columns = ["Open", "High", "Low", "Close", "Volume"]
                if len(df.columns) >= 5:
                    df.columns = expected_columns + list(df.columns[5:])
                    df = df[expected_columns]
                else:
                    raise ValueError(f"数据列数不足: {len(df.columns)}")

            # 保存到缓存
            if self.use_cache:
                df.to_csv(cache_file)

            return df

        except Exception as e:
            logger.error(f"下载 {symbol} 数据失败: {e}")
            raise

    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        为数据添加技术指标

        Args:
            df (pd.DataFrame): 原始OHLCV数据

        Returns:
            pd.DataFrame: 包含技术指标的数据
        """
        data = df.copy()

        # 基础价格数据 - 确保数据类型为float64
        high = data["High"].astype(float).values
        low = data["Low"].astype(float).values
        close = data["Close"].astype(float).values
        volume = data["Volume"].astype(float).values
        open_price = data["Open"].astype(float).values

        try:
            # 移动平均线
            data["MA5"] = talib.SMA(close, timeperiod=5)
            data["MA10"] = talib.SMA(close, timeperiod=10)
            data["MA20"] = talib.SMA(close, timeperiod=20)
            data["MA30"] = talib.SMA(close, timeperiod=30)
            data["MA60"] = talib.SMA(close, timeperiod=60)

            # 指数移动平均线
            data["EMA12"] = talib.EMA(close, timeperiod=12)
            data["EMA26"] = talib.EMA(close, timeperiod=26)

            # MACD
            macd, macdsignal, macdhist = talib.MACD(
                close, fastperiod=12, slowperiod=26, signalperiod=9
            )
            data["MACD"] = macd
            data["MACD_Signal"] = macdsignal
            data["MACD_Hist"] = macdhist

            # RSI
            data["RSI"] = talib.RSI(close, timeperiod=14)
            data["RSI_6"] = talib.RSI(close, timeperiod=6)
            data["RSI_21"] = talib.RSI(close, timeperiod=21)

            # 随机指标
            slowk, slowd = talib.STOCH(
                high, low, close, fastk_period=14, slowk_period=3, slowd_period=3
            )
            data["STOCH_K"] = slowk
            data["STOCH_D"] = slowd

            # 威廉指标
            data["WILLR"] = talib.WILLR(high, low, close, timeperiod=14)

            # 布林带
            upper, middle, lower = talib.BBANDS(
                close, timeperiod=20, nbdevup=2, nbdevdn=2
            )
            data["BB_Upper"] = upper
            data["BB_Middle"] = middle
            data["BB_Lower"] = lower
            data["BB_Width"] = (upper - lower) / middle
            data["BB_Position"] = (close - lower) / (upper - lower)

            # ATR - 真实波动范围
            data["ATR"] = talib.ATR(high, low, close, timeperiod=14)
            data["ATR_21"] = talib.ATR(high, low, close, timeperiod=21)

            # CCI - 顺势指标
            data["CCI"] = talib.CCI(high, low, close, timeperiod=14)

            # 动量指标
            data["MOM"] = talib.MOM(close, timeperiod=10)

            # ROC - 变动率指标
            data["ROC"] = talib.ROC(close, timeperiod=10)

            # 成交量指标
            data["OBV"] = talib.OBV(close, volume)
            data["AD"] = talib.AD(high, low, close, volume)

            # 添加价格变化特征
            data["Price_Change"] = close / open_price - 1
            data["High_Low_Ratio"] = high / low
            data["Close_Open_Ratio"] = close / open_price

            # 添加滞后特征 (用于比较历史值)
            for col in ["Close", "MA5", "MA10", "MA20", "RSI", "MACD"]:
                if col in data.columns:
                    data[f"{col}_Lag1"] = data[col].shift(1)
                    data[f"{col}_Lag2"] = data[col].shift(2)
                    data[f"{col}_Lag5"] = data[col].shift(5)

            # 添加一些常用的技术分析组合
            data["MA5_MA20_Ratio"] = data["MA5"] / data["MA20"]
            data["Close_MA20_Ratio"] = data["Close"] / data["MA20"]
            data["RSI_SMA"] = talib.SMA(data["RSI"].values, timeperiod=5)

            # 删除前面的NaN值
            data = data.dropna()

            logger.info(f"技术指标计算完成，数据形状: {data.shape}")

            return data

        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            raise

    def add_boolean_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        添加预定义的布尔指标

        Args:
            df (pd.DataFrame): 包含技术指标的数据

        Returns:
            pd.DataFrame: 包含布尔指标的数据
        """
        data = df.copy()

        try:
            # === 趋势指标 ===
            # 移动平均线关系
            data["ma5_gt_ma10"] = data["MA5"] > data["MA10"]
            data["ma5_gt_ma20"] = data["MA5"] > data["MA20"]
            data["ma10_gt_ma20"] = data["MA10"] > data["MA20"]
            data["ma20_gt_ma60"] = data["MA20"] > data["MA60"]
            data["ma5_gt_ma60"] = data["MA5"] > data["MA60"]

            data["ma5_lt_ma10"] = data["MA5"] < data["MA10"]
            data["ma5_lt_ma20"] = data["MA5"] < data["MA20"]
            data["ma10_lt_ma20"] = data["MA10"] < data["MA20"]
            data["ma20_lt_ma60"] = data["MA20"] < data["MA60"]
            data["ma5_lt_ma60"] = data["MA5"] < data["MA60"]

            # 价格与移动平均线关系
            data["close_gt_ma5"] = data["Close"] > data["MA5"]
            data["close_gt_ma10"] = data["Close"] > data["MA10"]
            data["close_gt_ma20"] = data["Close"] > data["MA20"]
            data["close_gt_ma60"] = data["Close"] > data["MA60"]

            data["close_lt_ma5"] = data["Close"] < data["MA5"]
            data["close_lt_ma10"] = data["Close"] < data["MA10"]
            data["close_lt_ma20"] = data["Close"] < data["MA20"]
            data["close_lt_ma60"] = data["Close"] < data["MA60"]

            # 移动平均线多头/空头排列
            data["ma_bullish"] = (
                (data["MA5"] > data["MA10"])
                & (data["MA10"] > data["MA20"])
                & (data["MA20"] > data["MA60"])
            )
            data["ma_bearish"] = (
                (data["MA5"] < data["MA10"])
                & (data["MA10"] < data["MA20"])
                & (data["MA20"] < data["MA60"])
            )

            # === RSI指标 ===
            data["rsi_oversold"] = data["RSI"] < 30
            data["rsi_overbought"] = data["RSI"] > 70
            data["rsi_bullish"] = data["RSI"] > 50
            data["rsi_bearish"] = data["RSI"] < 50
            data["rsi_extreme_oversold"] = data["RSI"] < 20
            data["rsi_extreme_overbought"] = data["RSI"] > 80

            # RSI背离
            data["rsi_rising"] = data["RSI"] > data["RSI"].shift(1)
            data["rsi_falling"] = data["RSI"] < data["RSI"].shift(1)

            # === MACD指标 ===
            data["macd_bullish"] = data["MACD"] > data["MACD_Signal"]
            data["macd_bearish"] = data["MACD"] < data["MACD_Signal"]
            data["macd_positive"] = data["MACD"] > 0
            data["macd_negative"] = data["MACD"] < 0
            data["macd_hist_positive"] = data["MACD_Hist"] > 0
            data["macd_hist_negative"] = data["MACD_Hist"] < 0

            # MACD金叉死叉
            data["macd_golden_cross"] = (data["MACD"] > data["MACD_Signal"]) & (
                data["MACD"].shift(1) <= data["MACD_Signal"].shift(1)
            )
            data["macd_death_cross"] = (data["MACD"] < data["MACD_Signal"]) & (
                data["MACD"].shift(1) >= data["MACD_Signal"].shift(1)
            )

            # === 布林带指标 ===
            data["bb_squeeze"] = data["BB_Width"] < data["BB_Width"].rolling(20).mean()
            data["bb_expansion"] = (
                data["BB_Width"] > data["BB_Width"].rolling(20).mean()
            )
            data["close_above_bb_upper"] = data["Close"] > data["BB_Upper"]
            data["close_below_bb_lower"] = data["Close"] < data["BB_Lower"]
            data["close_near_bb_upper"] = data["BB_Position"] > 0.8
            data["close_near_bb_lower"] = data["BB_Position"] < 0.2

            # === KDJ/随机指标 ===
            data["kdj_oversold"] = data["STOCH_K"] < 20
            data["kdj_overbought"] = data["STOCH_K"] > 80
            data["kdj_bullish"] = data["STOCH_K"] > data["STOCH_D"]
            data["kdj_bearish"] = data["STOCH_K"] < data["STOCH_D"]

            # === 威廉指标 ===
            data["willr_oversold"] = data["WILLR"] < -80
            data["willr_overbought"] = data["WILLR"] > -20

            # === CCI指标 ===
            data["cci_oversold"] = data["CCI"] < -100
            data["cci_overbought"] = data["CCI"] > 100
            data["cci_bullish"] = data["CCI"] > 0
            data["cci_bearish"] = data["CCI"] < 0

            # === 价格形态指标 ===
            data["price_rising"] = data["Close"] > data["Close"].shift(1)
            data["price_falling"] = data["Close"] < data["Close"].shift(1)
            data["price_gap_up"] = data["Open"] > data["Close"].shift(1) * 1.02
            data["price_gap_down"] = data["Open"] < data["Close"].shift(1) * 0.98

            # 连续上涨/下跌
            data["consecutive_up_2"] = (data["Close"] > data["Close"].shift(1)) & (
                data["Close"].shift(1) > data["Close"].shift(2)
            )
            data["consecutive_down_2"] = (data["Close"] < data["Close"].shift(1)) & (
                data["Close"].shift(1) < data["Close"].shift(2)
            )

            # === 成交量指标 ===
            data["volume_surge"] = (
                data["Volume"] > data["Volume"].rolling(20).mean() * 1.5
            )
            data["volume_dry"] = (
                data["Volume"] < data["Volume"].rolling(20).mean() * 0.5
            )
            data["volume_above_avg"] = (
                data["Volume"] > data["Volume"].rolling(20).mean()
            )

            # === 波动率指标 ===
            data["high_volatility"] = data["ATR"] > data["ATR"].rolling(20).mean() * 1.2
            data["low_volatility"] = data["ATR"] < data["ATR"].rolling(20).mean() * 0.8

            # === 组合条件 ===
            # 强势突破
            data["strong_breakout"] = (
                data["close_gt_ma20"]
                & data["ma5_gt_ma10"]
                & data["rsi_bullish"]
                & data["volume_surge"]
            )

            # 弱势破位
            data["weak_breakdown"] = (
                ~data["close_gt_ma20"]
                & ~data["ma5_gt_ma10"]
                & data["rsi_bearish"]
                & data["volume_surge"]
            )

            # 超跌反弹
            data["oversold_bounce"] = (
                data["rsi_oversold"]
                & data["bb_squeeze"]
                & data["close_below_bb_lower"]
                & data["price_rising"]
            )

            # 超买回调
            data["overbought_pullback"] = (
                data["rsi_overbought"]
                & data["close_above_bb_upper"]
                & data["price_falling"]
            )

            # 金叉买入
            data["golden_cross_buy"] = (
                data["macd_golden_cross"]
                & data["ma5_gt_ma10"]
                & data["rsi_bullish"]
                & data["volume_above_avg"]
            )

            # 死叉卖出
            data["death_cross_sell"] = (
                data["macd_death_cross"] & ~data["ma5_gt_ma10"] & data["rsi_bearish"]
            )

            # 只保留OHLCV和布尔指标，移除技术指标以提高效率
            ohlcv_columns = ["Open", "High", "Low", "Close", "Volume"]
            boolean_columns = [col for col in data.columns if col not in df.columns]

            # 保留OHLCV和布尔指标
            final_columns = ohlcv_columns + boolean_columns
            data = data[final_columns]

            logger.info(
                f"布尔指标计算完成，保留 {len(ohlcv_columns)} 个OHLCV列和 {len(boolean_columns)} 个布尔指标"
            )
            return data

        except Exception as e:
            logger.error(f"布尔指标计算失败: {e}")
            raise

    def get_boolean_indicators(self) -> List[str]:
        """
        获取所有可用的布尔指标列表

        Returns:
            List[str]: 布尔指标名称列表
        """
        boolean_indicators = [
            # 趋势指标
            "ma5_gt_ma10",
            "ma5_gt_ma20",
            "ma10_gt_ma20",
            "ma20_gt_ma60",
            "ma5_gt_ma60",
            "ma5_lt_ma10",
            "ma5_lt_ma20",
            "ma10_lt_ma20",
            "ma20_lt_ma60",
            "ma5_lt_ma60",
            "close_gt_ma5",
            "close_gt_ma10",
            "close_gt_ma20",
            "close_gt_ma60",
            "close_lt_ma5",
            "close_lt_ma10",
            "close_lt_ma20",
            "close_lt_ma60",
            "ma_bullish",
            "ma_bearish",
            # RSI指标
            "rsi_oversold",
            "rsi_overbought",
            "rsi_bullish",
            "rsi_bearish",
            "rsi_extreme_oversold",
            "rsi_extreme_overbought",
            "rsi_rising",
            "rsi_falling",
            # MACD指标
            "macd_bullish",
            "macd_bearish",
            "macd_positive",
            "macd_negative",
            "macd_hist_positive",
            "macd_hist_negative",
            "macd_golden_cross",
            "macd_death_cross",
            # 布林带指标
            "bb_squeeze",
            "bb_expansion",
            "close_above_bb_upper",
            "close_below_bb_lower",
            "close_near_bb_upper",
            "close_near_bb_lower",
            # KDJ指标
            "kdj_oversold",
            "kdj_overbought",
            "kdj_bullish",
            "kdj_bearish",
            # 威廉指标
            "willr_oversold",
            "willr_overbought",
            # CCI指标
            "cci_oversold",
            "cci_overbought",
            "cci_bullish",
            "cci_bearish",
            # 价格形态
            "price_rising",
            "price_falling",
            "price_gap_up",
            "price_gap_down",
            "consecutive_up_2",
            "consecutive_down_2",
            # 成交量指标
            "volume_surge",
            "volume_dry",
            "volume_above_avg",
            # 波动率指标
            "high_volatility",
            "low_volatility",
            # 组合条件
            "strong_breakout",
            "weak_breakdown",
            "oversold_bounce",
            "overbought_pullback",
            "golden_cross_buy",
            "death_cross_sell",
        ]

        return boolean_indicators

    def get_available_features(self) -> List[str]:
        """
        获取可用于构建信号的特征列表（包含布尔指标）

        Returns:
            list: 特征名称列表
        """
        # 基础价格特征
        price_features = ["Open", "High", "Low", "Close", "Volume"]

        # 技术指标特征
        technical_features = [
            "MA5",
            "MA10",
            "MA20",
            "MA30",
            "MA60",
            "EMA12",
            "EMA26",
            "MACD",
            "MACD_Signal",
            "MACD_Hist",
            "RSI",
            "RSI_6",
            "RSI_21",
            "RSI_SMA",
            "STOCH_K",
            "STOCH_D",
            "WILLR",
            "BB_Upper",
            "BB_Middle",
            "BB_Lower",
            "BB_Width",
            "BB_Position",
            "ATR",
            "ATR_21",
            "CCI",
            "MOM",
            "ROC",
            "OBV",
            "AD",
            "Price_Change",
            "High_Low_Ratio",
            "Close_Open_Ratio",
            "MA5_MA20_Ratio",
            "Close_MA20_Ratio",
        ]

        # 滞后特征
        lag_features = []
        base_features = ["Close", "MA5", "MA10", "MA20", "RSI", "MACD"]
        for feature in base_features:
            for lag in [1, 2, 5]:
                lag_features.append(f"{feature}_Lag{lag}")

        # 布尔指标
        boolean_features = self.get_boolean_indicators()

        return price_features + technical_features + lag_features + boolean_features

    def validate_data_quality(self, data: pd.DataFrame, symbol: str) -> bool:
        """
        验证数据质量

        Args:
            data (pd.DataFrame): 数据
            symbol (str): 股票代码

        Returns:
            bool: 数据是否合格
        """
        try:
            # 检查数据长度
            if len(data) < 100:
                logger.warning(f"{symbol}: 数据长度不足100天")
                return False

            # 检查关键列是否存在
            required_cols = ["Open", "High", "Low", "Close", "Volume"]
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                logger.error(f"{symbol}: 缺少关键列 {missing_cols}")
                return False

            # 检查是否有过多的NaN值
            nan_ratio = data.isnull().sum().sum() / (len(data) * len(data.columns))
            if nan_ratio > 0.1:  # 超过10%的NaN值
                logger.warning(f"{symbol}: NaN值比例过高 ({nan_ratio:.2%})")
                return False

            # 检查价格数据的合理性
            if (data["High"] < data["Low"]).any():
                logger.error(f"{symbol}: 存在最高价低于最低价的异常数据")
                return False

            if (data["Close"] <= 0).any() or (data["Volume"] < 0).any():
                logger.error(f"{symbol}: 存在非正价格或负成交量")
                return False

            logger.info(f"{symbol}: 数据质量验证通过")
            return True

        except Exception as e:
            logger.error(f"{symbol}: 数据质量验证出错 - {e}")
            return False

    def get_data_summary(self, data: pd.DataFrame, symbol: str) -> Dict:
        """
        获取数据摘要信息

        Args:
            data (pd.DataFrame): 数据
            symbol (str): 股票代码

        Returns:
            dict: 数据摘要
        """
        boolean_cols = [
            col for col in data.columns if col in self.get_boolean_indicators()
        ]

        summary = {
            "symbol": symbol,
            "start_date": data.index[0].strftime("%Y-%m-%d"),
            "end_date": data.index[-1].strftime("%Y-%m-%d"),
            "total_days": len(data),
            "total_columns": len(data.columns),
            "boolean_indicators": len(boolean_cols),
            "price_range": {
                "min": float(data["Close"].min()),
                "max": float(data["Close"].max()),
                "mean": float(data["Close"].mean()),
            },
            "volume_stats": {
                "mean": float(data["Volume"].mean()),
                "std": float(data["Volume"].std()),
            },
            "missing_values": int(data.isnull().sum().sum()),
            "boolean_indicator_names": boolean_cols[:10],  # 只显示前10个
        }

        return summary


class DataValidator:
    """数据验证器，用于数据质量检查和异常检测"""

    @staticmethod
    def detect_outliers(
        data: pd.DataFrame, column: str, method: str = "iqr"
    ) -> pd.Series:
        """
        检测异常值

        Args:
            data (pd.DataFrame): 数据
            column (str): 列名
            method (str): 检测方法 ('iqr', 'zscore')

        Returns:
            pd.Series: 异常值标记
        """
        if method == "iqr":
            Q1 = data[column].quantile(0.25)
            Q3 = data[column].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            return (data[column] < lower_bound) | (data[column] > upper_bound)

        elif method == "zscore":
            z_scores = np.abs((data[column] - data[column].mean()) / data[column].std())
            return z_scores > 3

        else:
            raise ValueError(f"未知的异常检测方法: {method}")

    @staticmethod
    def check_data_consistency(data: pd.DataFrame) -> List[str]:
        """
        检查数据一致性

        Args:
            data (pd.DataFrame): 数据

        Returns:
            list: 一致性问题列表
        """
        issues = []

        # 检查OHLC关系
        if (
            "Open" in data.columns
            and "High" in data.columns
            and "Low" in data.columns
            and "Close" in data.columns
        ):

            # High应该是最高价
            if not (data["High"] >= data["Open"]).all():
                issues.append("存在开盘价高于最高价的情况")
            if not (data["High"] >= data["Close"]).all():
                issues.append("存在收盘价高于最高价的情况")

            # Low应该是最低价
            if not (data["Low"] <= data["Open"]).all():
                issues.append("存在开盘价低于最低价的情况")
            if not (data["Low"] <= data["Close"]).all():
                issues.append("存在收盘价低于最低价的情况")

        return issues
