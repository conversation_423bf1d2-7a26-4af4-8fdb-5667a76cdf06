#!/usr/bin/env python3
"""
策略测试模块
提供策略回测功能
"""

import os
import json
import pandas as pd
import numpy as np
from loguru import logger
from datetime import datetime
from typing import Dict, List, Optional, Any
from ..utils.environment_setup import EnvironmentSetup
from .data_manager import DataManager
from .evaluator import Evaluator


class StrategyTester:
    """策略测试器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        
        if config is None:
            env_setup = EnvironmentSetup()
            config = env_setup.load_config("quick_start")
            
            if config is None:
                # 创建默认配置
                config = {
                    "data_config": {
                        "cache_dir": "data_cache",
                        "use_cache": True
                    },
                    "eval_config": {
                        "initial_capital": 100000,
                        "commission": 0.001,
                        "risk_free_rate": 0.02
                    }
                }
        
        self.config = config
        self.data_manager = DataManager(config.get("data_config", {}))
        self.evaluator = Evaluator(config.get("eval_config", {}))
    
    def test_strategy(self, 
                     symbol: str,
                     buy_signal: str,
                     sell_signal: str,
                     start_date: str = "2023-01-01",
                     end_date: str = "2023-12-31") -> Dict[str, Any]:
        """测试策略"""
        try:
            logger.info(f"Testing strategy for {symbol}")
            logger.info(f"Buy signal: {buy_signal}")
            logger.info(f"Sell signal: {sell_signal}")
            
            # 加载数据
            data = self.data_manager.load_data(symbol, start_date, end_date)
            
            if data.empty:
                raise ValueError(f"No data available for {symbol}")
            
            # 生成信号
            signals = self._generate_signals(data, buy_signal, sell_signal)
            
            # 评估策略
            results = self.evaluator.evaluate_strategy(data, signals)
            
            # 添加基本信息
            results.update({
                "symbol": symbol,
                "buy_signal": buy_signal,
                "sell_signal": sell_signal,
                "start_date": start_date,
                "end_date": end_date,
                "test_date": datetime.now().isoformat()
            })
            
            return results
            
        except Exception as e:
            logger.error(f"Strategy testing failed: {e}")
            raise
    
    def _generate_signals(self, data: pd.DataFrame, buy_signal: str, sell_signal: str) -> pd.Series:
        """生成交易信号"""
        signals = pd.Series(0, index=data.index)
        
        try:
            # 解析买入信号
            buy_condition = self._parse_signal(buy_signal, data)
            if buy_condition is not None:
                signals[buy_condition] = 1
            
            # 解析卖出信号
            sell_condition = self._parse_signal(sell_signal, data)
            if sell_condition is not None:
                signals[sell_condition] = -1
                
        except Exception as e:
            logger.error(f"Error generating signals: {e}")
            raise
        
        return signals
    
    def _parse_signal(self, signal_expr: str, data: pd.DataFrame) -> Optional[pd.Series]:
        """解析信号表达式"""
        try:
            # 简单的信号解析，支持基本的条件表达式
            if "rsi_oversold" in signal_expr.lower():
                # RSI超卖信号
                rsi = self._calculate_rsi(data['Close'])
                return rsi < 30
            elif "rsi_overbought" in signal_expr.lower():
                # RSI超买信号
                rsi = self._calculate_rsi(data['Close'])
                return rsi > 70
            elif "ma" in signal_expr.lower():
                # 移动平均线信号
                return self._parse_ma_signal(signal_expr, data)
            elif ">" in signal_expr or "<" in signal_expr:
                # 简单比较信号
                return self._parse_comparison_signal(signal_expr, data)
            else:
                # 默认返回None
                return None
                
        except Exception as e:
            logger.error(f"Error parsing signal '{signal_expr}': {e}")
            return None
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _parse_ma_signal(self, signal_expr: str, data: pd.DataFrame) -> pd.Series:
        """解析移动平均线信号"""
        try:
            # 示例: "close > ma(close, 20)"
            if "close > ma" in signal_expr.lower():
                # 提取周期
                import re
                match = re.search(r'ma\(.*?,(\d+)\)', signal_expr)
                if match:
                    period = int(match.group(1))
                    ma = data['Close'].rolling(window=period).mean()
                    return data['Close'] > ma
            elif "close < ma" in signal_expr.lower():
                match = re.search(r'ma\(.*?,(\d+)\)', signal_expr)
                if match:
                    period = int(match.group(1))
                    ma = data['Close'].rolling(window=period).mean()
                    return data['Close'] < ma
        except Exception as e:
            logger.error(f"Error parsing MA signal: {e}")
        
        return pd.Series(False, index=data.index)
    
    def _parse_comparison_signal(self, signal_expr: str, data: pd.DataFrame) -> pd.Series:
        """解析简单比较信号"""
        try:
            # 示例: "close > 100"
            if "close >" in signal_expr.lower():
                value = float(signal_expr.split(">")[1].strip())
                return data['Close'] > value
            elif "close <" in signal_expr.lower():
                value = float(signal_expr.split("<")[1].strip())
                return data['Close'] < value
        except Exception as e:
            logger.error(f"Error parsing comparison signal: {e}")
        
        return pd.Series(False, index=data.index)
    
    def test_multiple_strategies(self, 
                               strategies: List[Dict[str, str]],
                               symbol: str,
                               start_date: str = "2023-01-01",
                               end_date: str = "2023-12-31") -> List[Dict[str, Any]]:
        """测试多个策略"""
        results = []
        
        for i, strategy in enumerate(strategies):
            try:
                logger.info(f"Testing strategy {i+1}/{len(strategies)}")
                result = self.test_strategy(
                    symbol=symbol,
                    buy_signal=strategy['buy_signal'],
                    sell_signal=strategy['sell_signal'],
                    start_date=start_date,
                    end_date=end_date
                )
                results.append(result)
            except Exception as e:
                logger.error(f"Strategy {i+1} failed: {e}")
                results.append({
                    "error": str(e),
                    "strategy": strategy
                })
        
        return results
    
    def compare_strategies(self, 
                          strategies: List[Dict[str, str]],
                          symbol: str,
                          start_date: str = "2023-01-01",
                          end_date: str = "2023-12-31") -> pd.DataFrame:
        """比较多个策略"""
        results = self.test_multiple_strategies(strategies, symbol, start_date, end_date)
        
        # 转换为DataFrame进行比较
        comparison_data = []
        for i, result in enumerate(results):
            if "error" not in result:
                comparison_data.append({
                    "strategy": f"Strategy {i+1}",
                    "buy_signal": strategies[i]['buy_signal'],
                    "sell_signal": strategies[i]['sell_signal'],
                    "total_return": result.get('total_return', 0),
                    "annual_return": result.get('annual_return', 0),
                    "max_drawdown": result.get('max_drawdown', 0),
                    "sharpe_ratio": result.get('sharpe_ratio', 0),
                    "win_rate": result.get('win_rate', 0),
                    "trade_count": result.get('trade_count', 0)
                })
        
        return pd.DataFrame(comparison_data)


def test_strategy_interactive():
    """交互式策略测试"""
    print("交互式策略测试")
    print("-" * 50)

    # 获取用户输入
    default_buy_signal = "rsi_oversold"
    default_sell_signal = "rsi_overbought"
    
    buy_signal = input(f"请输入买入信号表达式 (例: {default_buy_signal}): ")
    sell_signal = input(f"请输入卖出信号表达式 (例: {default_sell_signal}): ")
    
    if not buy_signal:
        buy_signal = default_buy_signal
    if not sell_signal:
        sell_signal = default_sell_signal

    symbol = input("请输入股票代码 (默认: 000001.SZ): ").strip().upper()
    if not symbol:
        symbol = "000001.SZ"

    # 测试策略
    tester = StrategyTester()
    results = tester.test_strategy(symbol, buy_signal, sell_signal)
    
    # 输出结果
    print("\n=== 策略回测结果 ===")
    print(f"股票代码: {symbol}")
    print(f"买入信号: {buy_signal}")
    print(f"卖出信号: {sell_signal}")
    print(f"总收益率: {results.get('total_return', 0):.2%}")
    print(f"年化收益率: {results.get('annual_return', 0):.2%}")
    print(f"最大回撤: {results.get('max_drawdown', 0):.2%}")
    print(f"夏普比率: {results.get('sharpe_ratio', 0):.4f}")
    print(f"胜率: {results.get('win_rate', 0):.2%}")
    print(f"交易次数: {results.get('trade_count', 0)}")


def test_custom_strategy(buy_expr: str, sell_expr: str, symbol: str = "000001.SZ"):
    """测试自定义策略"""
    print(f"测试自定义策略: 买入={buy_expr}, 卖出={sell_expr}")
    
    tester = StrategyTester()
    results = tester.test_strategy(symbol, buy_expr, sell_expr)
    
    # 输出结果
    print("\n=== 策略回测结果 ===")
    print(f"股票代码: {symbol}")
    print(f"总收益率: {results.get('total_return', 0):.2%}")
    print(f"年化收益率: {results.get('annual_return', 0):.2%}")
    print(f"最大回撤: {results.get('max_drawdown', 0):.2%}")
    print(f"夏普比率: {results.get('sharpe_ratio', 0):.4f}")
    print(f"胜率: {results.get('win_rate', 0):.2%}")
    print(f"交易次数: {results.get('trade_count', 0)}")
    
    return results
