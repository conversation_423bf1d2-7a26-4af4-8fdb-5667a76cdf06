# reporter.py - 训练报告生成器模块
"""
生成详细的训练报告，包括最优策略表现、图表分析等
支持HTML和PDF格式输出
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from .path_manager import get_path_manager
from typing import Dict, List
from datetime import datetime
from loguru import logger
import os

plt.style.use("default")
sns.set_palette("husl")


class Reporter:
    """训练报告生成器"""

    def __init__(self, config: Dict):
        """
        初始化报告生成器

        Args:
            config (dict): 报告配置参数
        """
        self.config = config
        self.include_plots = config.get("include_plots", True)
        self.plot_format = config.get("plot_format", "png")

        # 设置中文字体
        plt.rcParams["font.sans-serif"] = ["SimHei", "DejaVu Sans"]
        plt.rcParams["axes.unicode_minus"] = False

    def generate_report(self, results: List[Dict]) -> str:
        """
        生成完整的训练报告

        Args:
            results (list): 策略结果列表

        Returns:
            str: HTML报告内容
        """
        logger.info("开始生成训练报告")

        try:
            # 生成HTML报告
            html_content = self._generate_html_report(results)

            logger.info("训练报告生成完成")
            return html_content

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return self._generate_error_report(str(e))

    def _generate_html_report(self, results: List[Dict]) -> str:
        """生成HTML报告"""

        # HTML头部
        html_parts = [
            self._get_html_header(),
            self._get_report_title(),
            self._get_summary_section(results),
            self._get_top_strategies_section(results),
            self._get_performance_comparison_section(results),
            self._get_detailed_analysis_section(results),
            self._get_html_footer(),
        ]

        return "\n".join(html_parts)

    def _get_html_header(self) -> str:
        """获取HTML头部"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易信号优化训练报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
        }
        .summary-box {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .strategy-card {
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #ffffff;
        }
        .strategy-rank {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
            font-weight: bold;
        }
        .metric-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metric-table th, .metric-table td {
            border: 1px solid #bdc3c7;
            padding: 8px 12px;
            text-align: center;
        }
        .metric-table th {
            background-color: #3498db;
            color: white;
        }
        .metric-table tr:nth-child(even) {
            background-color: #ecf0f1;
        }
        .positive { color: #27ae60; font-weight: bold; }
        .negative { color: #e74c3c; font-weight: bold; }
        .plot-container {
            text-align: center;
            margin: 20px 0;
        }
        .expression {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 15px;
        }
    </style>
</head>
<body>
<div class="container">
"""

    def _get_report_title(self) -> str:
        """获取报告标题"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return f"""
<h1>🚀 交易信号基因编程优化报告</h1>
<div class="summary-box">
    <p><strong>生成时间:</strong> {current_time}</p>
    <p><strong>优化方法:</strong> 遗传编程 (Genetic Programming)</p>
    <p><strong>框架版本:</strong> TradingSignalGP v1.0</p>
</div>
"""

    def _get_summary_section(self, results: List[Dict]) -> str:
        """获取摘要部分"""
        if not results:
            return "<h2>📊 训练摘要</h2><p>没有找到有效的策略结果。</p>"

        # 计算统计信息
        total_strategies = len(results)

        # 训练集指标
        train_returns = [r["train_metrics"].get("annual_return", 0) for r in results]
        train_sharpe = [r["train_metrics"].get("sharpe_ratio", 0) for r in results]
        train_calmar = [r["train_metrics"].get("calmar_ratio", 0) for r in results]

        # 测试集指标
        test_returns = [r["test_metrics"].get("annual_return", 0) for r in results]
        test_sharpe = [r["test_metrics"].get("sharpe_ratio", 0) for r in results]
        test_calmar = [r["test_metrics"].get("calmar_ratio", 0) for r in results]

        # 最佳策略
        best_strategy = results[0] if results else None

        summary_html = f"""
<h2>📊 训练摘要</h2>
<div class="summary-box">
    <h3>总体统计</h3>
    <ul>
        <li><strong>策略数量:</strong> {total_strategies}</li>
        <li><strong>最佳策略年化收益率:</strong> <span class="{'positive' if test_returns[0] > 0 else 'negative'}">{test_returns[0]:.2%}</span></li>
        <li><strong>最佳策略夏普比率:</strong> {test_sharpe[0]:.3f}</li>
        <li><strong>最佳策略卡玛比率:</strong> {test_calmar[0]:.3f}</li>
    </ul>
    
    <h3>性能分布</h3>
    <table class="metric-table">
        <tr>
            <th>指标</th>
            <th>数据集</th>
            <th>平均值</th>
            <th>最大值</th>
            <th>最小值</th>
            <th>标准差</th>
        </tr>
        <tr>
            <td rowspan="2">年化收益率</td>
            <td>训练集</td>
            <td>{np.mean(train_returns):.2%}</td>
            <td>{np.max(train_returns):.2%}</td>
            <td>{np.min(train_returns):.2%}</td>
            <td>{np.std(train_returns):.2%}</td>
        </tr>
        <tr>
            <td>测试集</td>
            <td>{np.mean(test_returns):.2%}</td>
            <td>{np.max(test_returns):.2%}</td>
            <td>{np.min(test_returns):.2%}</td>
            <td>{np.std(test_returns):.2%}</td>
        </tr>
        <tr>
            <td rowspan="2">夏普比率</td>
            <td>训练集</td>
            <td>{np.mean(train_sharpe):.3f}</td>
            <td>{np.max(train_sharpe):.3f}</td>
            <td>{np.min(train_sharpe):.3f}</td>
            <td>{np.std(train_sharpe):.3f}</td>
        </tr>
        <tr>
            <td>测试集</td>
            <td>{np.mean(test_sharpe):.3f}</td>
            <td>{np.max(test_sharpe):.3f}</td>
            <td>{np.min(test_sharpe):.3f}</td>
            <td>{np.std(test_sharpe):.3f}</td>
        </tr>
    </table>
</div>
"""
        return summary_html

    def _get_top_strategies_section(self, results: List[Dict]) -> str:
        """获取最优策略部分"""
        if not results:
            return ""

        strategies_html = "<h2>🏆 前10名最优策略</h2>\n"

        for i, result in enumerate(results[:10]):
            rank = result.get("rank", i + 1)
            buy_signal = result.get("buy_signal", "N/A")
            sell_signal = result.get("sell_signal", "N/A")

            train_metrics = result.get("train_metrics", {})
            test_metrics = result.get("test_metrics", {})

            # 策略卡片
            strategy_card = f"""
<div class="strategy-card">
    <span class="strategy-rank">#{rank}</span>
    <h3>策略 {rank}</h3>
    
    <h4>📈 交易信号</h4>
    <div class="expression">
        <strong>买入条件:</strong> {buy_signal}<br>
        <strong>卖出条件:</strong> {sell_signal}
    </div>
    
    <h4>📊 性能指标</h4>
    <table class="metric-table">
        <tr>
            <th>指标</th>
            <th>训练集</th>
            <th>测试集</th>
        </tr>
        <tr>
            <td>年化收益率</td>
            <td class="{'positive' if train_metrics.get('annual_return', 0) > 0 else 'negative'}">{train_metrics.get('annual_return', 0):.2%}</td>
            <td class="{'positive' if test_metrics.get('annual_return', 0) > 0 else 'negative'}">{test_metrics.get('annual_return', 0):.2%}</td>
        </tr>
        <tr>
            <td>夏普比率</td>
            <td>{train_metrics.get('sharpe_ratio', 0):.3f}</td>
            <td>{test_metrics.get('sharpe_ratio', 0):.3f}</td>
        </tr>
        <tr>
            <td>卡玛比率</td>
            <td>{train_metrics.get('calmar_ratio', 0):.3f}</td>
            <td>{test_metrics.get('calmar_ratio', 0):.3f}</td>
        </tr>
        <tr>
            <td>最大回撤</td>
            <td class="negative">{train_metrics.get('max_drawdown', 0):.2%}</td>
            <td class="negative">{test_metrics.get('max_drawdown', 0):.2%}</td>
        </tr>
        <tr>
            <td>交易次数</td>
            <td>{train_metrics.get('total_trades', 0)}</td>
            <td>{test_metrics.get('total_trades', 0)}</td>
        </tr>
        <tr>
            <td>胜率</td>
            <td>{train_metrics.get('win_rate', 0):.1%}</td>
            <td>{test_metrics.get('win_rate', 0):.1%}</td>
        </tr>
    </table>
</div>
"""
            strategies_html += strategy_card

        return strategies_html

    def _get_performance_comparison_section(self, results: List[Dict]) -> str:
        """获取性能比较部分"""
        if not results:
            return ""

        # 生成比较图表
        comparison_plots = self._create_performance_plots(results)

        section_html = f"""
<h2>📈 性能比较分析</h2>
<div class="plot-container">
    {comparison_plots}
</div>
"""
        return section_html

    def _get_detailed_analysis_section(self, results: List[Dict]) -> str:
        """获取详细分析部分"""
        if not results:
            return ""

        # 过拟合分析
        overfitting_analysis = self._analyze_overfitting(results)

        # 稳定性分析
        stability_analysis = self._analyze_stability(results)

        section_html = f"""
<h2>🔬 详细分析</h2>

<h3>过拟合分析</h3>
<div class="summary-box">
    {overfitting_analysis}
</div>

<h3>策略稳定性分析</h3>
<div class="summary-box">
    {stability_analysis}
</div>

<h3>建议</h3>
<div class="summary-box">
    {self._generate_recommendations(results)}
</div>
"""
        return section_html

    def _create_performance_plots(self, results: List[Dict]) -> str:
        """创建性能比较图表"""
        try:
            # 提取数据
            ranks = [r.get("rank", i + 1) for i, r in enumerate(results)]
            train_returns = [
                r["train_metrics"].get("annual_return", 0) * 100 for r in results
            ]
            test_returns = [
                r["test_metrics"].get("annual_return", 0) * 100 for r in results
            ]
            train_sharpe = [r["train_metrics"].get("sharpe_ratio", 0) for r in results]
            test_sharpe = [r["test_metrics"].get("sharpe_ratio", 0) for r in results]

            # 创建图表
            fig = make_subplots(
                rows=2,
                cols=2,
                subplot_titles=(
                    "年化收益率比较",
                    "夏普比率比较",
                    "训练vs测试收益率",
                    "风险收益散点图",
                ),
                specs=[[{}, {}], [{}, {}]],
            )

            # 年化收益率比较
            fig.add_trace(
                go.Bar(
                    x=ranks[:10],
                    y=train_returns[:10],
                    name="训练集",
                    marker_color="lightblue",
                ),
                row=1,
                col=1,
            )
            fig.add_trace(
                go.Bar(
                    x=ranks[:10],
                    y=test_returns[:10],
                    name="测试集",
                    marker_color="lightcoral",
                ),
                row=1,
                col=1,
            )

            # 夏普比率比较
            fig.add_trace(
                go.Scatter(
                    x=ranks[:10],
                    y=train_sharpe[:10],
                    mode="lines+markers",
                    name="训练集夏普",
                    line=dict(color="blue"),
                ),
                row=1,
                col=2,
            )
            fig.add_trace(
                go.Scatter(
                    x=ranks[:10],
                    y=test_sharpe[:10],
                    mode="lines+markers",
                    name="测试集夏普",
                    line=dict(color="red"),
                ),
                row=1,
                col=2,
            )

            # 训练vs测试散点图
            fig.add_trace(
                go.Scatter(
                    x=train_returns,
                    y=test_returns,
                    mode="markers",
                    name="策略表现",
                    marker=dict(size=8, opacity=0.6),
                ),
                row=2,
                col=1,
            )
            # 添加对角线
            min_val = min(min(train_returns), min(test_returns))
            max_val = max(max(train_returns), max(test_returns))
            fig.add_trace(
                go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode="lines",
                    name="理想线",
                    line=dict(dash="dash"),
                ),
                row=2,
                col=1,
            )

            # 风险收益散点图
            test_volatility = [
                r["test_metrics"].get("volatility", 0) * 100 for r in results
            ]
            fig.add_trace(
                go.Scatter(
                    x=test_volatility,
                    y=test_returns,
                    mode="markers",
                    text=[f"策略{r.get('rank', i+1)}" for i, r in enumerate(results)],
                    name="风险收益",
                    marker=dict(size=8, opacity=0.6),
                ),
                row=2,
                col=2,
            )

            # 更新布局
            fig.update_layout(
                height=800, showlegend=True, title_text="策略性能综合分析"
            )
            fig.update_xaxes(title_text="策略排名", row=1, col=1)
            fig.update_yaxes(title_text="年化收益率(%)", row=1, col=1)
            fig.update_xaxes(title_text="策略排名", row=1, col=2)
            fig.update_yaxes(title_text="夏普比率", row=1, col=2)
            fig.update_xaxes(title_text="训练集收益率(%)", row=2, col=1)
            fig.update_yaxes(title_text="测试集收益率(%)", row=2, col=1)
            fig.update_xaxes(title_text="波动率(%)", row=2, col=2)
            fig.update_yaxes(title_text="年化收益率(%)", row=2, col=2)

            # 转换为HTML
            plot_html = fig.to_html(include_plotlyjs="cdn")
            return plot_html

        except Exception as e:
            logger.error(f"创建图表失败: {e}")
            return "<p>图表生成失败</p>"

    def _analyze_overfitting(self, results: List[Dict]) -> str:
        """分析过拟合情况"""
        try:
            overfitting_scores = []

            for result in results:
                train_sharpe = result["train_metrics"].get("sharpe_ratio", 0)
                test_sharpe = result["test_metrics"].get("sharpe_ratio", 0)

                if train_sharpe > 0:
                    overfitting_score = (train_sharpe - test_sharpe) / train_sharpe
                    overfitting_scores.append(overfitting_score)

            if overfitting_scores:
                avg_overfitting = np.mean(overfitting_scores)
                severe_overfitting = len([s for s in overfitting_scores if s > 0.3])

                analysis = f"""
<p><strong>过拟合程度评估:</strong></p>
<ul>
    <li>平均过拟合程度: {avg_overfitting:.1%}</li>
    <li>严重过拟合策略数量: {severe_overfitting}/{len(results)}</li>
    <li>过拟合评级: {'<span class="negative">严重</span>' if avg_overfitting > 0.3 else '<span class="positive">轻微</span>' if avg_overfitting < 0.1 else '中等'}</li>
</ul>
<p><strong>建议:</strong> {'需要增加正则化或减少模型复杂度' if avg_overfitting > 0.2 else '过拟合程度在可接受范围内'}</p>
"""
                return analysis

        except Exception as e:
            logger.error(f"过拟合分析失败: {e}")

        return "<p>无法进行过拟合分析</p>"

    def _analyze_stability(self, results: List[Dict]) -> str:
        """分析策略稳定性"""
        try:
            # 分析收益率的变异系数
            test_returns = [r["test_metrics"].get("annual_return", 0) for r in results]
            test_sharpe = [r["test_metrics"].get("sharpe_ratio", 0) for r in results]

            if test_returns and np.mean(test_returns) != 0:
                return_cv = np.std(test_returns) / abs(np.mean(test_returns))
                sharpe_cv = (
                    np.std(test_sharpe) / abs(np.mean(test_sharpe))
                    if np.mean(test_sharpe) != 0
                    else 0
                )

                stability_rating = (
                    "高" if return_cv < 0.5 else "中" if return_cv < 1.0 else "低"
                )

                analysis = f"""
<p><strong>稳定性指标:</strong></p>
<ul>
    <li>收益率变异系数: {return_cv:.3f}</li>
    <li>夏普比率变异系数: {sharpe_cv:.3f}</li>
    <li>稳定性评级: <span class="{'positive' if stability_rating == '高' else 'negative' if stability_rating == '低' else ''}">{stability_rating}</span></li>
</ul>
"""
                return analysis

        except Exception as e:
            logger.error(f"稳定性分析失败: {e}")

        return "<p>无法进行稳定性分析</p>"

    def _generate_recommendations(self, results: List[Dict]) -> str:
        """生成建议"""
        if not results:
            return "<p>无法生成建议</p>"

        best_strategy = results[0]

        recommendations = ["<p><strong>基于分析结果的建议:</strong></p><ul>"]

        # 基于最佳策略表现给出建议
        test_sharpe = best_strategy["test_metrics"].get("sharpe_ratio", 0)
        test_return = best_strategy["test_metrics"].get("annual_return", 0)
        max_dd = best_strategy["test_metrics"].get("max_drawdown", 0)

        if test_sharpe > 1.0:
            recommendations.append("<li>✅ 最佳策略夏普比率优秀，建议重点关注</li>")
        elif test_sharpe > 0.5:
            recommendations.append("<li>⚠️ 最佳策略夏普比率中等，可考虑进一步优化</li>")
        else:
            recommendations.append(
                "<li>❌ 最佳策略夏普比率较低，建议重新训练或调整参数</li>"
            )

        if abs(max_dd) > 0.2:
            recommendations.append("<li>⚠️ 最大回撤较大，建议增加风险控制措施</li>")

        if test_return < 0:
            recommendations.append("<li>❌ 策略在测试集上亏损，不建议实盘交易</li>")

        recommendations.append("</ul>")

        return "\n".join(recommendations)

    def _get_html_footer(self) -> str:
        """获取HTML尾部"""
        return (
            """
<div class="footer">
    <p>📊 基于遗传编程的交易信号优化系统 | 生成于 """
            + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            + """</p>
</div>
</div>
</body>
</html>
"""
        )

    def _generate_error_report(self, error_message: str) -> str:
        """生成错误报告"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>报告生成错误</title>
</head>
<body>
    <h1>报告生成失败</h1>
    <p>错误信息: {error_message}</p>
    <p>请检查数据和配置后重试。</p>
</body>
</html>
"""

    def save_report(self, html_content: str, filename: str = None):
        """
        保存HTML报告到文件

        Args:
            html_content (str): HTML内容
            filename (str): 文件名，如果为None则使用默认名称
        """
        try:
            if filename is None:
                # 使用路径管理器保存到会话目录
                from .path_manager import get_path_manager
                path_manager = get_path_manager()
                path_manager.save_report(html_content)
                logger.info(f"报告已保存至会话目录")
            else:
                # 兼容旧版本，保存到reports目录
                filepath = os.path.join(self.output_dir, filename)
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(html_content)
                logger.info(f"报告已保存至: {filepath}")

        except Exception as e:
            logger.error(f"保存报告失败: {e}")

    def create_strategy_comparison_chart(self, results: List[Dict]) -> str:
        """创建策略比较雷达图"""
        try:
            if len(results) == 0:
                return ""

            # 选择前5个策略进行比较
            top_strategies = results[:5]

            # 准备数据
            categories = ["年化收益", "夏普比率", "卡玛比率", "胜率", "稳定性"]

            fig = go.Figure()

            for i, strategy in enumerate(top_strategies):
                test_metrics = strategy["test_metrics"]

                # 标准化指标到0-1范围
                values = [
                    min(test_metrics.get("annual_return", 0) * 10, 1),  # 年化收益*10
                    min(test_metrics.get("sharpe_ratio", 0) / 3, 1),  # 夏普比率/3
                    min(test_metrics.get("calmar_ratio", 0) / 3, 1),  # 卡玛比率/3
                    test_metrics.get("win_rate", 0),  # 胜率
                    max(1 - abs(test_metrics.get("max_drawdown", 0)) * 5, 0),  # 稳定性
                ]

                fig.add_trace(
                    go.Scatterpolar(
                        r=values,
                        theta=categories,
                        fill="toself",
                        name=f'策略{strategy.get("rank", i+1)}',
                    )
                )

            fig.update_layout(
                polar=dict(radialaxis=dict(visible=True, range=[0, 1])),
                showlegend=True,
                title="前5策略多维度比较",
            )

            return fig.to_html(include_plotlyjs="cdn")

        except Exception as e:
            logger.error(f"创建雷达图失败: {e}")
            return ""


class PDFReporter:
    """PDF报告生成器"""

    def __init__(self):
        logger = get_logger()

    def html_to_pdf(self, html_content: str, output_path: str):
        """将HTML转换为PDF"""
        try:
            import pdfkit

            options = {
                "page-size": "A4",
                "encoding": "UTF-8",
                "no-outline": None,
                "margin-top": "0.75in",
                "margin-right": "0.75in",
                "margin-bottom": "0.75in",
                "margin-left": "0.75in",
            }

            pdfkit.from_string(html_content, output_path, options=options)
            logger.info(f"PDF报告已生成: {output_path}")

        except ImportError:
            logger.warning("pdfkit未安装，无法生成PDF报告")
        except Exception as e:
            logger.error(f"PDF生成失败: {e}")


class ExcelReporter:
    """Excel报告生成器"""

    def __init__(self):
        logger = get_logger()

    def generate_excel_report(self, results: List[Dict], output_path: str):
        """生成Excel格式的详细报告"""
        try:
            with pd.ExcelWriter(output_path, engine="openpyxl") as writer:

                # 策略汇总表
                summary_data = []
                for result in results:
                    row = {
                        "排名": result.get("rank", 0),
                        "买入信号": result.get("buy_signal", ""),
                        "卖出信号": result.get("sell_signal", ""),
                        # 测试集指标
                        "测试_年化收益率": result["test_metrics"].get(
                            "annual_return", 0
                        ),
                        "测试_夏普比率": result["test_metrics"].get("sharpe_ratio", 0),
                        "测试_卡玛比率": result["test_metrics"].get("calmar_ratio", 0),
                        "测试_最大回撤": result["test_metrics"].get("max_drawdown", 0),
                        "测试_交易次数": result["test_metrics"].get("total_trades", 0),
                        "测试_胜率": result["test_metrics"].get("win_rate", 0),
                        # 训练集指标
                        "训练_年化收益率": result["train_metrics"].get(
                            "annual_return", 0
                        ),
                        "训练_夏普比率": result["train_metrics"].get("sharpe_ratio", 0),
                        "训练_卡玛比率": result["train_metrics"].get("calmar_ratio", 0),
                        "训练_最大回撤": result["train_metrics"].get("max_drawdown", 0),
                        "训练_交易次数": result["train_metrics"].get("total_trades", 0),
                        "训练_胜率": result["train_metrics"].get("win_rate", 0),
                    }
                    summary_data.append(row)

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name="策略汇总", index=False)

                # 性能对比表
                comparison_data = {
                    "指标": ["年化收益率", "夏普比率", "卡玛比率", "最大回撤", "胜率"],
                    "训练集_平均": [
                        summary_df["训练_年化收益率"].mean(),
                        summary_df["训练_夏普比率"].mean(),
                        summary_df["训练_卡玛比率"].mean(),
                        summary_df["训练_最大回撤"].mean(),
                        summary_df["训练_胜率"].mean(),
                    ],
                    "测试集_平均": [
                        summary_df["测试_年化收益率"].mean(),
                        summary_df["测试_夏普比率"].mean(),
                        summary_df["测试_卡玛比率"].mean(),
                        summary_df["测试_最大回撤"].mean(),
                        summary_df["测试_胜率"].mean(),
                    ],
                    "最优值": [
                        summary_df["测试_年化收益率"].max(),
                        summary_df["测试_夏普比率"].max(),
                        summary_df["测试_卡玛比率"].max(),
                        summary_df["测试_最大回撤"].max(),
                        summary_df["测试_胜率"].max(),
                    ],
                }

                comparison_df = pd.DataFrame(comparison_data)
                comparison_df.to_excel(writer, sheet_name="性能对比", index=False)

                # 详细指标表（包含更多指标）
                detailed_data = []
                for result in results[:20]:  # 只保存前20个策略的详细信息
                    test_metrics = result["test_metrics"]
                    train_metrics = result["train_metrics"]

                    detailed_row = {
                        "排名": result.get("rank", 0),
                        "买入信号": result.get("buy_signal", ""),
                        "卖出信号": result.get("sell_signal", ""),
                        # 更多测试集指标
                        "测试_总收益率": test_metrics.get("total_return", 0),
                        "测试_年化收益率": test_metrics.get("annual_return", 0),
                        "测试_波动率": test_metrics.get("volatility", 0),
                        "测试_夏普比率": test_metrics.get("sharpe_ratio", 0),
                        "测试_卡玛比率": test_metrics.get("calmar_ratio", 0),
                        "测试_最大回撤": test_metrics.get("max_drawdown", 0),
                        "测试_交易次数": test_metrics.get("total_trades", 0),
                        "测试_胜率": test_metrics.get("win_rate", 0),
                        "测试_平均盈利": test_metrics.get("avg_win", 0),
                        "测试_平均亏损": test_metrics.get("avg_loss", 0),
                        "测试_盈利因子": test_metrics.get("profit_factor", 0),
                        "测试_恢复因子": test_metrics.get("recovery_factor", 0),
                        # 训练集对应指标
                        "训练_总收益率": train_metrics.get("total_return", 0),
                        "训练_年化收益率": train_metrics.get("annual_return", 0),
                        "训练_波动率": train_metrics.get("volatility", 0),
                        "训练_夏普比率": train_metrics.get("sharpe_ratio", 0),
                        "训练_卡玛比率": train_metrics.get("calmar_ratio", 0),
                        "训练_最大回撤": train_metrics.get("max_drawdown", 0),
                        "训练_交易次数": train_metrics.get("total_trades", 0),
                        "训练_胜率": train_metrics.get("win_rate", 0),
                        "训练_平均盈利": train_metrics.get("avg_win", 0),
                        "训练_平均亏损": train_metrics.get("avg_loss", 0),
                        "训练_盈利因子": train_metrics.get("profit_factor", 0),
                        "训练_恢复因子": train_metrics.get("recovery_factor", 0),
                    }
                    detailed_data.append(detailed_row)

                detailed_df = pd.DataFrame(detailed_data)
                detailed_df.to_excel(writer, sheet_name="详细指标", index=False)

            logger.info(f"Excel报告已生成: {output_path}")

        except Exception as e:
            logger.error(f"Excel报告生成失败: {e}")
