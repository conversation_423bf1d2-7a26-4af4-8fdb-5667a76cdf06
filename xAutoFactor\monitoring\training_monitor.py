#!/usr/bin/env python3
"""
训练监控模块
提供实时训练进度监控功能
"""

import json
import psutil
from datetime import datetime
from typing import Dict, List, Optional, Any

from loguru import logger
from ..utils.path_manager import get_path_manager


class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self):
        
        self.running_processes = {}  # 存储正在运行的训练进程信息
    
    def get_running_processes(self) -> List[Dict[str, Any]]:
        """获取正在运行的训练进程"""
        processes = []
        
        # 使用路径管理器获取检查点目录
        path_manager = get_path_manager()
        
        try:
            status_file = path_manager.get_status_path()
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
            
            # 检查进程是否还在运行
            process_id = status.get("process_id")
            if process_id and self._is_process_running(process_id):
                # 更新运行时间
                if status.get("start_time"):
                    start_time = datetime.fromisoformat(status["start_time"])
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    status["elapsed_time"] = elapsed_time
                    
                    # 估算剩余时间
                    current_gen = status.get("current_generation", 0)
                    total_gens = status.get("total_generations", 100)
                    if current_gen > 0 and total_gens > 0:
                        avg_time_per_gen = elapsed_time / current_gen
                        remaining_gens = total_gens - current_gen
                        status["estimated_remaining"] = avg_time_per_gen * remaining_gens
                
                status["status"] = "running"
                processes.append(status)
            else:
                # 进程已结束，更新状态
                status["status"] = "completed"
                status["end_time"] = datetime.now().isoformat()
                
                # 保存更新后的状态
                with open(status_file, 'w', encoding='utf-8') as f:
                    json.dump(status, f, indent=2, ensure_ascii=False)
                
                processes.append(status)
                
        except Exception as e:
            logger.error(f"Error reading status file {status_file}: {e}")
        
        return processes
    
    def get_process_info(self, process_id: str) -> Optional[Dict[str, Any]]:
        """获取特定进程的详细信息"""
        processes = self.get_running_processes()
        
        for process in processes:
            if process.get("process_id") == process_id:
                return process
        
        return None
    
    def _is_process_running(self, process_id: int) -> bool:
        """检查进程是否在运行"""
        try:
            process = psutil.Process(process_id)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False
    
    def get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源使用情况"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_gb": memory.used / (1024**3),
                "memory_total_gb": memory.total / (1024**3),
                "disk_percent": disk.percent,
                "disk_used_gb": disk.used / (1024**3),
                "disk_total_gb": disk.total / (1024**3),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting system resources: {e}")
            return {}
    
    def get_training_logs(self, process_id: str, lines: int = 100) -> List[str]:
        """获取训练日志"""
        path_manager = get_path_manager()
        log_file = path_manager.get_log_path()
        
        if not log_file.exists():
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            logger.error(f"Error reading log file {log_file}: {e}")
            return []
    
    def cleanup_finished_processes(self) -> int:
        """清理已完成的进程状态文件"""
        cleaned_count = 0
        try:
            path_manager = get_path_manager()
            status_file = path_manager.get_status_path()
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
            
            process_id = status.get("process_id")
            if process_id and not self._is_process_running(process_id):
                # 进程已结束，可以清理状态文件
                status_file.unlink()
                cleaned_count += 1
                logger.info(f"Cleaned up status file: {status_file}")
                
        except Exception as e:
            logger.error(f"Error processing status file {status_file}: {e}")
        
        return cleaned_count


class TrainingMonitorAPI:
    """训练监控API类，用于Web界面"""
    
    def __init__(self):
        self.monitor = TrainingMonitor()
    
    def get_all_processes(self) -> List[Dict[str, Any]]:
        """获取所有训练进程"""
        return self.monitor.get_running_processes()
    
    def get_process_details(self, process_id: str) -> Dict[str, Any]:
        """获取进程详细信息"""
        process_info = self.monitor.get_process_info(process_id)
        if not process_info:
            return {"error": "Process not found"}
        
        # 添加系统资源信息
        system_resources = self.monitor.get_system_resources()
        
        # 添加日志信息
        logs = self.monitor.get_training_logs(process_id)
        
        return {
            **process_info,
            "system_resources": system_resources,
            "recent_logs": logs
        }
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return self.monitor.get_system_resources()
