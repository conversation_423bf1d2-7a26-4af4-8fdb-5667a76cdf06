import pickle

# 检查DNA格式
data = pickle.load(open('user_data/logs/20250818_162854/ck_16.pkl', 'rb'))
print('Population size:', len(data['population']))

print('\nDNA格式分析:')
for i, ind in enumerate(data['population'][:5]):
    print(f'{i+1}: {type(ind)} = {ind}')
    print(f'    str(ind) = {str(ind)}')
    print(f'    repr(ind) = {repr(ind)}')
    if hasattr(ind, '__len__'):
        print(f'    len(ind) = {len(ind)}')
    print()

# 检查是否有其他键
print('检查点数据键:', list(data.keys()))
if 'halloffame' in data:
    print('\nHall of Fame:')
    for i, ind in enumerate(data['halloffame'][:3]):
        print(f'{i+1}: {ind}')
