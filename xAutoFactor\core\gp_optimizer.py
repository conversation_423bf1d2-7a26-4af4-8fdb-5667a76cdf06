# gp_optimizer.py - 遗传编程优化器模块
"""
基于DEAP库的遗传编程优化器
实现交易信号表达式的进化优化，支持复杂性控制和中断恢复
"""

import numpy as np
import random
from loguru import logger
from typing import List, Dict, Tuple, Optional, Callable
import os
import json
from datetime import datetime
from pathlib import Path
from deap import base, creator, tools, gp
import warnings

warnings.filterwarnings("ignore")
from ..utils.path_manager import get_path_manager
from .signal_generator import SignalGenerator


class GeneticProgrammingOptimizer:
    """遗传编程优化器"""

    def __init__(self, config: Dict):
        """
        初始化优化器

        Args:
            config (dict): 优化配置参数
        """
        self.config = config
        self.population_size = config.get("population_size", 100)
        self.generations = config.get("generations", 50)
        self.max_depth = config.get("max_depth", 5)
        self.min_depth = config.get("min_depth", 1)

        # 遗传算法参数
        self.crossover_rate = config.get("crossover_rate", 0.8)
        self.mutation_rate = config.get("mutation_rate", 0.2)
        self.tournament_size = config.get("tournament_size", 3)
        self.elite_size = config.get("elite_size", 5)

        # 复杂性控制参数
        self.complexity_penalty = config.get("complexity_penalty", 0.01)
        self.max_complexity = config.get("max_complexity", 20)

        # 早停参数
        self.early_stopping = config.get("early_stopping", True)
        self.patience = config.get("patience", 10)
        self.min_improvement = config.get("min_improvement", 0.000001)

        # 检查点参数
        self.checkpoint_frequency = config.get("checkpoint_frequency", 10)
        self.progress_callback = None

        # 初始化DEAP环境
        self.setup_deap()
        self.path_manager = get_path_manager()

    def setup_deap(self):
        """设置DEAP遗传编程环境"""

        # 清除可能存在的旧类型
        if hasattr(creator, "FitnessMax"):
            del creator.FitnessMax
        if hasattr(creator, "Individual"):
            del creator.Individual

        # 创建适应度类（最大化）
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))

        # 创建个体类
        creator.create("Individual", list, fitness=creator.FitnessMax)

        # 创建工具箱
        self.toolbox = base.Toolbox()

        # 注册信号生成器（假设已有）
        self.signal_generator = SignalGenerator({"max_depth": self.max_depth})

        # 注册个体生成函数
        self.toolbox.register("individual", self._create_individual)
        self.toolbox.register(
            "population", tools.initRepeat, list, self.toolbox.individual
        )

        # 注册遗传操作
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutate)
        self.toolbox.register(
            "select", tools.selTournament, tournsize=self.tournament_size
        )

        # 注册评估函数（将在evolve方法中设置）
        self.toolbox.register("evaluate", lambda x: (0.0,))  # 占位符

    def _create_individual(self):
        """创建一个个体（包含买入和卖出信号）"""
        buy_expr, sell_expr = self.signal_generator.generate_individual()
        individual = creator.Individual([buy_expr, sell_expr])
        return individual

    def _crossover(self, ind1, ind2):
        """交叉操作"""
        try:
            # 复制个体
            new_ind1 = creator.Individual([ind1[0], ind1[1]])
            new_ind2 = creator.Individual([ind2[0], ind2[1]])

            # 随机选择交叉买入信号还是卖出信号
            if random.random() < 0.5:
                # 交叉买入信号 - 重新生成
                new_ind1[0] = self.signal_generator.generate_simple_signal("buy")
                new_ind2[0] = self.signal_generator.generate_simple_signal("buy")
            else:
                # 交叉卖出信号 - 重新生成
                new_ind1[1] = self.signal_generator.generate_simple_signal("sell")
                new_ind2[1] = self.signal_generator.generate_simple_signal("sell")

            return new_ind1, new_ind2

        except Exception as e:
            logger.debug(f"交叉操作失败: {e}")
            return ind1, ind2

    def _mutate(self, individual):
        """变异操作"""
        try:
            # 复制个体
            new_individual = creator.Individual([individual[0], individual[1]])

            # 随机选择变异买入信号还是卖出信号
            if random.random() < 0.5:
                # 变异买入信号 - 重新生成
                new_individual[0] = self.signal_generator.generate_simple_signal("buy")
            else:
                # 变异卖出信号 - 重新生成
                new_individual[1] = self.signal_generator.generate_simple_signal("sell")

            return (new_individual,)

        except Exception as e:
            logger.debug(f"变异操作失败: {e}")
            return (individual,)

    def _evaluate_with_complexity_penalty(
        self, individual, evaluate_func: Callable
    ) -> Tuple[float]:
        """带复杂性惩罚的评估函数"""
        try:
            # 基础适应度
            base_fitness = evaluate_func(individual)[0]

            # 计算复杂性（基于字符串表达式的长度）
            buy_expr = individual[0]
            sell_expr = individual[1]
            complexity = len(buy_expr) + len(sell_expr)

            # 应用复杂性惩罚
            if complexity > self.max_complexity * 10:  # 调整阈值
                # 超过最大复杂性，严重惩罚
                penalized_fitness = base_fitness * 0.1
            else:
                # 线性惩罚
                penalty = self.complexity_penalty * complexity / 100  # 调整惩罚系数
                penalized_fitness = base_fitness - penalty

            return (penalized_fitness,)

        except Exception as e:
            logger.error(f"评估个体时出错: {e}")
            return (0.0,)

    def evolve(
        self,
        evaluate_func: Callable,
        population: Optional[List] = None,
        start_generation: int = 0,
        halloffame: Optional[tools.HallOfFame] = None,
        progress_callback: Optional[Callable] = None,
        evaluation_cache: Optional[dict] = None,
    ) -> Tuple[List, Dict]:
        """
        运行遗传算法进化

        Args:
            evaluate_func: 个体评估函数
            population: 初始种群（可选，用于恢复训练）
            start_generation: 起始代数（用于恢复训练）
            halloffame: 名人堂（用于恢复训练）
            progress_callback: 进度回调函数

        Returns:
            tuple: (最优个体列表, 统计信息)
        """
        logger.info(f"开始遗传算法进化，目标代数: {self.generations}")

        # 设置评估函数
        self.toolbox.register(
            "evaluate",
            lambda ind: self._evaluate_with_complexity_penalty(ind, evaluate_func),
        )

        # 初始化种群
        if population is None:
            population = self.toolbox.population(n=self.population_size)
            logger.info(f"创建初始种群，大小: {self.population_size}")
        else:
            logger.info(f"使用提供的种群，大小: {len(population)}")

        # 初始化名人堂 - 保存所有个体用于报告生成
        if halloffame is None:
            # 使用更大的Hall of Fame来保存更多个体
            hall_size = max(self.population_size, 100)  # 至少保存100个个体
            halloffame = tools.HallOfFame(hall_size)

        # 初始化统计
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)
        stats.register("std", np.std)

        # 记录统计信息
        logbook = tools.Logbook()
        logbook.header = ["gen", "nevals"] + (stats.fields if stats else [])

        # 评估初始种群（如果需要）
        invalid_ind = [ind for ind in population if not ind.fitness.valid]
        if invalid_ind:
            logger.info(f"评估 {len(invalid_ind)} 个初始个体")
            fitnesses = map(self.toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit

        # 更新名人堂
        halloffame.update(population)

        # 记录初始统计
        record = stats.compile(population) if stats else {}
        logbook.record(gen=start_generation, nevals=len(invalid_ind), **record)
        logger.info(f"代数 {start_generation}: {record}")

        # 早停参数 - 改为基于平均适应度
        best_avg_fitness = -np.inf
        generations_without_improvement = 0

        # 主进化循环
        generation = start_generation  # 初始化generation变量
        for generation in range(start_generation + 1, self.generations + 1):
            try:
                # 选择
                offspring = self.toolbox.select(population, len(population))
                offspring = list(map(self.toolbox.clone, offspring))

                # 交叉
                for child1, child2 in zip(offspring[::2], offspring[1::2]):
                    if random.random() < self.crossover_rate:
                        self.toolbox.mate(child1, child2)
                        del child1.fitness.values
                        del child2.fitness.values

                # 变异
                for mutant in offspring:
                    if random.random() < self.mutation_rate:
                        self.toolbox.mutate(mutant)
                        del mutant.fitness.values

                # 评估新个体
                invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
                if invalid_ind:
                    fitnesses = map(self.toolbox.evaluate, invalid_ind)
                    for ind, fit in zip(invalid_ind, fitnesses):
                        ind.fitness.values = fit

                # 替换种群
                population[:] = offspring

                # 更新名人堂
                halloffame.update(population)

                # 记录统计
                record = stats.compile(population) if stats else {}
                logbook.record(gen=generation, nevals=len(invalid_ind), **record)

                # 检查是否有改进 - 改为基于平均适应度
                current_best = (
                    halloffame[0].fitness.values[0] if len(halloffame) > 0 else -np.inf
                )
                current_avg = record.get("avg", 0)

                if current_avg > best_avg_fitness + self.min_improvement:
                    best_avg_fitness = current_avg
                    generations_without_improvement = 0
                else:
                    generations_without_improvement += 1

                logger.info(
                    f"代数 {generation}: 最优={current_best:.4f}, 平均={current_avg:.4f}"
                )

                # 调用进度回调函数
                if progress_callback:
                    try:
                        progress_callback(
                            generation=generation,
                            total_generations=self.generations,
                            best_fitness=current_best,
                            avg_fitness=record.get("avg", 0),
                            population_size=len(population),
                            phase="训练中",
                        )
                    except Exception as e:
                        logger.warning(f"进度回调函数出错: {e}")

                # 早停检查
                if (
                    self.early_stopping
                    and generations_without_improvement >= self.patience
                ):
                    logger.info(f"触发早停，{self.patience}代无改进")
                    break

                # 保存检查点 - 每代都保存
                self._save_checkpoint(
                    generation, population, halloffame, logbook, evaluation_cache
                )

            except Exception as e:
                logger.error(f"进化第{generation}代时出错: {e}")
                continue

        logger.info("遗传算法进化完成")

        # 更新训练状态为已完成
        final_best_fitness = (
            halloffame[0].fitness.values[0] if len(halloffame) > 0 else 0.0
        )
        self._update_training_status_completed(generation, final_best_fitness)

        # 返回结果
        best_individuals = list(halloffame)
        evolution_stats = {
            "logbook": logbook,
            "final_generation": generation,
            "best_fitness": final_best_fitness,
            "convergence_info": {
                "converged": generations_without_improvement >= self.patience,
                "generations_without_improvement": generations_without_improvement,
            },
        }

        return best_individuals, evolution_stats

    def _save_checkpoint(
        self,
        generation: int,
        population: List,
        halloffame: tools.HallOfFame,
        logbook: tools.Logbook,
        evaluation_results: dict = None,
    ):
        """保存检查点"""
        try:
            # 使用路径管理器获取checkpoint路径
            checkpoint_file = self.path_manager.get_checkpoint_path(
                generation=generation
            )

            # 计算当前统计信息
            fitnesses = [
                ind.fitness.values[0] for ind in population if ind.fitness.valid
            ]
            best_fitness = max(fitnesses) if fitnesses else 0.0
            avg_fitness = np.mean(fitnesses) if fitnesses else 0.0

            checkpoint_data = {
                "generation": generation,
                "population": population,
                "halloffame": halloffame,
                "logbook": logbook,
                "config": self.config,
                "evaluation_results": evaluation_results or {},  # 保存回测结果
                "metadata": {
                    "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
                    "best_fitness": best_fitness,
                    "avg_fitness": avg_fitness,
                    "population_size": len(population),
                    "valid_individuals": len(fitnesses),
                    "fitness_std": np.std(fitnesses) if len(fitnesses) > 1 else 0.0,
                    "fitness_min": min(fitnesses) if fitnesses else 0.0,
                    "fitness_max": max(fitnesses) if fitnesses else 0.0,
                },
            }

            # 使用路径管理器保存checkpoint
            self.path_manager.save_checkpoint(generation, checkpoint_data)

            # 更新训练状态文件
            self._update_training_status(generation, best_fitness, avg_fitness)

        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def _update_training_status(
        self, generation: int, best_fitness: float, avg_fitness: float
    ):
        """更新训练状态文件"""
        try:
            # 使用路径管理器获取状态文件
            current_status = self.path_manager.get_status_dict()

            # 更新状态信息
            current_status.update(
                {
                    "current_generation": generation,
                    "total_generations": self.generations,
                    "best_fitness": best_fitness,
                    "avg_fitness": avg_fitness,
                    "status": "running",
                    "last_update": datetime.now().isoformat(),
                }
            )

            # 添加配置文件信息
            if hasattr(self, "config") and self.config:
                # 如果config是字典，从中获取config_name
                if isinstance(self.config, dict):
                    config_name = self.config.get("config_name", "unknown")
                else:
                    config_name = getattr(self.config, "config_name", "unknown")
                current_status["config_name"] = config_name

            # 计算运行时间
            if "start_time" in current_status:
                start_time = datetime.fromisoformat(current_status["start_time"])
                elapsed_time = (datetime.now() - start_time).total_seconds()
                current_status["elapsed_time"] = elapsed_time

                # 估算剩余时间
                if generation > 0:
                    avg_time_per_gen = elapsed_time / generation
                    remaining_gens = self.generations - generation
                    current_status["estimated_remaining"] = (
                        avg_time_per_gen * remaining_gens
                    )

            # 保存更新后的状态
            self.path_manager.save_status(current_status)

            logger.debug(
                f"训练状态已更新: 第{generation}代，最佳适应度={best_fitness:.6f}"
            )

        except Exception as e:
            logger.error(f"更新训练状态失败: {e}")

    def _update_training_status_completed(self, generation: int, best_fitness: float):
        """更新训练状态为已完成"""
        try:
            current_status = self.path_manager.get_status_dict()
            # 更新状态信息为已完成
            current_status.update(
                {
                    "current_generation": generation,
                    "total_generations": self.generations,
                    "best_fitness": best_fitness,
                    "status": "completed",
                    "last_update": datetime.now().isoformat(),
                }
            )

            # 计算总运行时间
            if "start_time" in current_status:
                start_time = datetime.fromisoformat(current_status["start_time"])
                elapsed_time = (datetime.now() - start_time).total_seconds()
                current_status["elapsed_time"] = elapsed_time
                current_status["estimated_remaining"] = 0.0  # 已完成，剩余时间为0

            self.path_manager.save_status(current_status)

            logger.info(
                f"训练状态已更新为已完成: 第{generation}代，最终最佳适应度={best_fitness:.6f}"
            )

        except Exception as e:
            logger.error(f"更新训练完成状态失败: {e}")

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def get_diversity_metrics(self, population: List) -> Dict:
        """计算种群多样性指标"""
        try:
            # 计算适应度多样性
            fitnesses = [
                ind.fitness.values[0] for ind in population if ind.fitness.valid
            ]

            diversity_metrics = {
                "fitness_std": np.std(fitnesses) if fitnesses else 0,
                "fitness_range": (
                    np.max(fitnesses) - np.min(fitnesses) if fitnesses else 0
                ),
                "population_size": len(population),
            }

            # 计算表达式多样性（简化版）
            expressions = []
            for ind in population:
                try:
                    buy_expr, sell_expr = (
                        self.signal_generator.individual_to_expressions(ind)
                    )
                    expressions.append(f"{buy_expr}|{sell_expr}")
                except:
                    continue

            unique_expressions = len(set(expressions))
            diversity_metrics["expression_diversity"] = (
                unique_expressions / len(expressions) if expressions else 0
            )

            return diversity_metrics

        except Exception as e:
            logger.error(f"计算多样性指标失败: {e}")
            return {}

    def adaptive_parameters(self, generation: int, stats: Dict):
        """自适应调整遗传算法参数"""
        try:
            # 根据进化进展调整变异率
            if "std" in stats and stats["std"] < 0.01:
                # 适应度标准差很小，增加变异率促进探索
                self.mutation_rate = min(0.5, self.mutation_rate * 1.1)
                logger.debug(f"增加变异率至 {self.mutation_rate:.3f}")

            # 根据代数调整交叉率
            if generation > self.generations * 0.8:
                # 后期降低交叉率，增加局部搜索
                self.crossover_rate = max(0.5, self.crossover_rate * 0.95)
                logger.debug(f"降低交叉率至 {self.crossover_rate:.3f}")

        except Exception as e:
            logger.error(f"自适应参数调整失败: {e}")


class MultiObjectiveOptimizer(GeneticProgrammingOptimizer):
    """多目标优化器，同时优化收益和风险"""

    def setup_deap(self):
        """设置多目标DEAP环境"""

        # 创建多目标适应度类（最大化收益，最小化风险）
        if not hasattr(creator, "FitnessMulti"):
            creator.create("FitnessMulti", base.Fitness, weights=(1.0, -1.0))

        # 创建个体类
        if not hasattr(creator, "IndividualMulti"):
            creator.create("IndividualMulti", list, fitness=creator.FitnessMulti)

        # 创建工具箱
        self.toolbox = base.Toolbox()

        # 注册信号生成器
        self.signal_generator = SignalGenerator({"max_depth": self.max_depth})

        # 注册个体生成函数
        self.toolbox.register("individual", self._create_multi_individual)
        self.toolbox.register(
            "population", tools.initRepeat, list, self.toolbox.individual
        )

        # 注册多目标选择算子
        self.toolbox.register("select", tools.selNSGA2)
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutate)

    def _create_multi_individual(self):
        """创建多目标个体"""
        buy_tree, sell_tree = self.signal_generator.generate_individual()
        individual = creator.IndividualMulti([buy_tree, sell_tree])
        return individual

    def multi_objective_evaluate(
        self, individual, evaluate_func: Callable
    ) -> Tuple[float, float]:
        """多目标评估函数"""
        try:
            # 获取基础指标
            metrics = evaluate_func(individual)

            if metrics is None:
                return (0.0, 1.0)  # 低收益，高风险

            # 目标1：最大化夏普比率
            sharpe_ratio = metrics.get("sharpe_ratio", 0.0)

            # 目标2：最小化最大回撤（取绝对值）
            max_drawdown = abs(metrics.get("max_drawdown", 1.0))

            return (sharpe_ratio, max_drawdown)

        except Exception as e:
            logger.error(f"多目标评估失败: {e}")
            return (0.0, 1.0)
