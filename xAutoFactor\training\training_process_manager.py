#!/usr/bin/env python3
"""
训练进程管理器
负责启动、停止和管理训练进程，与checkpoint系统集成
"""

import os
import sys
import json
import time
import signal
import subprocess
import psutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, List
import uuid

from loguru import logger
from ..utils.path_manager import get_path_manager


class TrainingProcessManager:
    """训练进程管理器"""

    def __init__(self, config: Dict):
        """
        初始化进程管理器

        Args:
            config (dict): 配置参数
        """
        self.config = config

        self.process_id = None
        self.process = None
        self.status_file = None

        # 使用路径管理器创建训练会话
        path_manager = get_path_manager()
        self.training_id = path_manager.create_training_session()
        self.path_manager = path_manager

        # 注意：不再使用旧的checkpoints目录结构

        # 状态文件路径（现在在会话目录中）
        self.status_file = path_manager.get_status_path()

        # 重新配置日志系统，使其将日志保存到会话目录
        from ..utils.logger import reset_logging, get_logger

        reset_logging()

        # 更新日志文件路径到会话目录
        log_file_path = path_manager.get_log_path()
        logger_instance = get_logger()
        logger_instance.update_log_file(str(log_file_path))

    @classmethod
    def check_running_tasks(cls) -> List[Dict]:
        """
        检查当前是否有运行中的训练任务

        Returns:
            List[Dict]: 运行中的任务列表，每个任务包含training_id, process_id, status等信息
        """
        running_tasks = []

        try:
            # 获取路径管理器
            path_manager = get_path_manager()
            
            # 检查当前是否有活跃的session
            current_session_id = path_manager.get_current_session_id()
            if not current_session_id:
                return running_tasks

            # 检查session的状态文件
            session_dir = path_manager.get_user_data_root() / "logs" / current_session_id
            status_file = session_dir / "status.json"
            
            if status_file.exists():
                try:
                    with open(status_file, "r", encoding="utf-8") as f:
                        status = json.load(f)

                    # 检查状态是否为运行中
                    if status.get("status") in ["running", "starting"]:
                        training_id = status.get("training_id")
                        process_id = status.get("process_id")

                        # 验证进程是否真的在运行
                        if process_id and cls._is_process_running(process_id):
                            running_tasks.append(
                                {
                                    "training_id": training_id,
                                    "process_id": process_id,
                                    "status": status.get("status"),
                                    "start_time": status.get("start_time"),
                                    "current_generation": status.get(
                                        "current_generation", 0
                                    ),
                                    "total_generations": status.get(
                                        "total_generations", 0
                                    ),
                                    "status_file": str(status_file),
                                }
                            )
                        else:
                            # 进程不存在，更新状态为停止
                            cls._update_status_file(status_file, {"status": "stopped"})

                except Exception as e:
                    # 如果读取状态文件失败，跳过
                    logger.error(f"读取状态文件失败: {e}")

            return running_tasks

        except Exception as e:
            logger.error(f"检查运行中任务失败: {e}")
            return running_tasks

    @classmethod
    def _is_process_running(cls, process_id: int) -> bool:
        """
        检查进程是否在运行

        Args:
            process_id (int): 进程ID

        Returns:
            bool: 进程是否在运行
        """
        try:
            process = psutil.Process(process_id)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False

    @classmethod
    def _update_status_file(cls, status_file: Path, updates: Dict):
        """
        更新状态文件

        Args:
            status_file (Path): 状态文件路径
            updates (Dict): 要更新的状态信息
        """
        try:
            if status_file.exists():
                with open(status_file, "r", encoding="utf-8") as f:
                    current_status = json.load(f)

                current_status.update(updates)

                with open(status_file, "w", encoding="utf-8") as f:
                    json.dump(current_status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"更新状态文件失败: {e}")

    @classmethod
    def stop_all_running_tasks(cls) -> bool:
        """
        停止所有运行中的训练任务

        Returns:
            bool: 是否成功停止所有任务
        """
        running_tasks = cls.check_running_tasks()
        success = True

        for task in running_tasks:
            try:
                process_id = task["process_id"]
                training_id = task["training_id"]

                # 终止进程
                process = psutil.Process(process_id)
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    process.kill()
                    process.wait()

                # 更新状态文件
                status_file = Path(task["status_file"])
                cls._update_status_file(
                    status_file,
                    {
                        "status": "stopped",
                        "end_time": datetime.now().isoformat(),
                        "last_update": datetime.now().isoformat(),
                    },
                )

                logger.info(f"已停止训练任务: {training_id} (PID: {process_id})")

            except Exception as e:
                logger.error(f"停止训练任务失败: {e}")
                success = False

        return success

    def start_training(self, config_file: str = "") -> bool:
        """
        启动训练进程

        Args:
            config_file (str): 配置文件名称

        Returns:
            bool: 是否成功启动
        """
        return self._start_training_internal(config_file, None)

    def start_training_from_checkpoint(
        self, config_file: str, checkpoint_file: str
    ) -> bool:
        """
        从checkpoint启动训练进程

        Args:
            config_file (str): 配置文件名称
            checkpoint_file (str): checkpoint文件路径

        Returns:
            bool: 是否成功启动
        """
        return self._start_training_internal(config_file, checkpoint_file)

    def _start_training_internal(
        self, config_file: str, checkpoint_file: str = None
    ) -> bool:
        """
        内部启动训练进程方法

        Args:
            config_file (str): 配置文件名称
            checkpoint_file (str): checkpoint文件路径（可选）

        Returns:
            bool: 是否成功启动
        """
        try:
            # 检查是否有其他运行中的任务
            running_tasks = self.check_running_tasks()
            if running_tasks:
                logger.warning(f"检测到 {len(running_tasks)} 个运行中的训练任务:")
                for task in running_tasks:
                    logger.warning(
                        f"  - 训练ID: {task['training_id']}, 进程ID: {task['process_id']}, 状态: {task['status']}"
                    )
                logger.error("请先停止现有任务或等待其完成后再启动新任务")
                return False

            if self.is_running():
                logger.warning("训练进程已在运行中")
                return False

            # 创建训练脚本
            script_content = self._create_training_script(config_file, checkpoint_file)
            self.path_manager.save_train_script(script_content)
            script_path = self.path_manager.get_train_script_path()

            # 启动进程
            cmd = [sys.executable, script_path]
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=os.getcwd(),  # 确保工作目录正确
            )

            self.process_id = self.process.pid

            # 初始化状态
            status_data = {
                "training_id": self.training_id,
                "process_id": self.process_id,
                "start_time": datetime.now().isoformat(),
                "status": "starting",
                "current_generation": 0,
                "total_generations": self.config.get("generations", 100),
                "best_fitness": 0.0,
                "avg_fitness": 0.0,
                "elapsed_time": 0,
                "estimated_remaining": 0,
                "last_update": datetime.now().isoformat(),
                "config_file": config_file,
                "session_dir": str(self.path_manager.get_session_dir()),
            }
            self.path_manager.save_status(status_data)

            logger.info(
                f"训练进程已启动，PID: {self.process_id}, 训练ID: {self.training_id}"
            )
            return True

        except Exception as e:
            logger.error(f"启动训练进程失败: {e}")
            return False

    def stop_training(self) -> bool:
        """
        停止训练进程

        Returns:
            bool: 是否成功停止
        """
        try:
            if not self.is_running():
                logger.warning("没有运行中的训练进程")
                return False

            # 更新状态为停止中
            self.path_manager.save_status({"status": "stopping"})

            # 发送终止信号
            if self.process:
                self.process.terminate()

                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("进程未在10秒内结束，强制终止")
                    self.process.kill()
                    self.process.wait()

            # 更新状态
            self.path_manager.save_status(
                {
                    "status": "stopped",
                    "end_time": datetime.now().isoformat(),
                    "last_update": datetime.now().isoformat(),
                }
            )

            logger.info(f"训练进程已停止，PID: {self.process_id}")
            self.process = None
            self.process_id = None
            return True

        except Exception as e:
            logger.error(f"停止训练进程失败: {e}")
            return False

    def is_running(self) -> bool:
        """
        检查训练进程是否在运行

        Returns:
            bool: 是否在运行
        """
        if not self.process:
            return False

        # 检查进程是否还在运行
        if self.process.poll() is None:
            return True

        # 进程已结束，清理状态
        self.process = None
        self.process_id = None
        return False

    def get_status(self) -> Dict:
        """
        获取训练状态

        Returns:
            dict: 训练状态信息
        """
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, "r", encoding="utf-8") as f:
                    status = json.load(f)

                # 如果进程在运行，更新运行时间
                if self.is_running() and status.get("status") == "running":
                    start_time = datetime.fromisoformat(status["start_time"])
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    status["elapsed_time"] = elapsed_time
                    status["last_update"] = datetime.now().isoformat()

                    # 估算剩余时间
                    if status.get("current_generation", 0) > 0:
                        avg_time_per_gen = elapsed_time / status["current_generation"]
                        remaining_gens = (
                            status["total_generations"] - status["current_generation"]
                        )
                        status["estimated_remaining"] = (
                            avg_time_per_gen * remaining_gens
                        )

                return status
            else:
                return {"status": "not_started"}

        except Exception as e:
            logger.error(f"获取训练状态失败: {e}")
            return {"status": "error", "error": str(e)}

    def get_checkpoints(self) -> List[Dict]:
        """
        获取当前session的checkpoint信息

        Returns:
            List[Dict]: checkpoint列表
        """
        try:
            # 使用checkpoint_manager获取checkpoint信息
            from ..monitoring.checkpoint_manager import CheckpointManager
            
            checkpoint_manager = CheckpointManager()
            checkpoints = checkpoint_manager.get_checkpoints()
            
            # 转换格式以保持兼容性
            result = []
            for checkpoint in checkpoints:
                result.append({
                    "filename": checkpoint["filename"],
                    "filepath": checkpoint["path"],
                    "generation": checkpoint["generation"],
                    "timestamp": checkpoint["datetime"],
                    "size": checkpoint["size_kb"] * 1024,  # 转换为字节
                    "modified": datetime.fromtimestamp(checkpoint["timestamp"]).isoformat(),
                })
            
            # 按代数排序
            result.sort(key=lambda x: x["generation"])
            return result

        except Exception as e:
            logger.error(f"获取checkpoint列表失败: {e}")
            return []

    def _create_training_script(
        self, config_file: str, checkpoint_file: str = None
    ) -> str:
        """
        创建训练脚本内容

        Args:
            config_file (str): 配置文件名称
            checkpoint_file (str): checkpoint文件路径（可选）

        Returns:
            str: 脚本内容
        """
        checkpoint_param = f'"{checkpoint_file}"' if checkpoint_file else "None"

        script_content = f'''#!/usr/bin/env python3
"""
自动生成的训练脚本
训练ID: {self.training_id}
"""

import sys
import os

# 检查xAutoFactor包是否已安装
try:
    import xAutoFactor
except ImportError:
    print("错误: xAutoFactor包未安装")
    print("请运行以下命令安装:")
    print("pip install -e .")
    sys.exit(1)

# 导入训练脚本模块
try:
    from xAutoFactor.training.training_script import run_training_script
except ImportError:
    print("错误: 无法导入训练脚本模块")
    print("请确保xAutoFactor包已正确安装")
    sys.exit(1)

def main():
    """主函数"""
    training_id = "{self.training_id}"
    config_file = "{config_file}"
    status_file = r"{os.path.abspath(self.status_file)}"
    checkpoint_file = {checkpoint_param}
    
    # 运行训练
    success = run_training_script(config_file)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        return script_content

    def _update_status(self, updates: Dict):
        """
        更新训练状态

        Args:
            updates (dict): 要更新的状态信息
        """
        try:
            # 读取当前状态
            current_status = {}
            if self.status_file.exists():
                with open(self.status_file, "r", encoding="utf-8") as f:
                    current_status = json.load(f)

            # 更新状态
            current_status.update(updates)

            # 保存状态
            self.path_manager.save_status(current_status)

        except Exception as e:
            logger.error(f"更新训练状态失败: {e}")


def get_training_managers() -> List[TrainingProcessManager]:
    """
    获取所有训练管理器实例

    Returns:
        List[TrainingProcessManager]: 训练管理器列表
    """
    managers = []
    
    try:
        # 使用路径管理器获取所有session
        path_manager = get_path_manager()
        sessions = path_manager.list_sessions()

        for session in sessions:
            training_id = session["training_id"]
            
            # 创建管理器实例
            config = {}
            manager = TrainingProcessManager(config)
            manager.training_id = training_id
            
            # 设置状态文件路径
            session_dir = path_manager.get_user_data_root() / "logs" / training_id
            manager.status_file = session_dir / "status.json"

            managers.append(manager)

    except Exception as e:
        logger.error(f"获取训练管理器失败: {e}")

    return managers
