"""
xAutoFactor 监控模块
整合训练监控、结果查看等功能
"""

import os
import json
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any
from flask import Flask, render_template, jsonify, request
import psutil

from loguru import logger
from ..utils.path_manager import get_path_manager
from .checkpoint_manager import CheckpointManager
from .training_monitor import TrainingMonitor
from .independent_monitor import IndependentMonitor


class Monitor:
    """统一的监控管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        
        # 初始化路径管理器
        self.path_manager = get_path_manager()
        self.config_path = config_path or "monitor_config"
        self.checkpoint_manager = CheckpointManager()
        self.training_monitor = TrainingMonitor()
        self.independent_monitor = IndependentMonitor()
        
        # Flask应用
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='templates')
        
        self.setup_routes()
        
    def setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/')
        def index():
            """主页面 - 显示所有训练进程"""
            training_processes = self.get_all_training_processes()
            return render_template('monitor.html', 
                                 training_processes=training_processes)
        
        @self.app.route('/api/training_processes')
        def api_training_processes():
            """API: 获取所有训练进程状态"""
            return jsonify(self.get_all_training_processes())
        
        @self.app.route('/api/training_process/<process_id>')
        def api_training_process(process_id):
            """API: 获取特定训练进程详情"""
            process_info = self.get_training_process_info(process_id)
            return jsonify(process_info)
        
        @self.app.route('/api/checkpoints/<session_id>')
        def api_checkpoints(session_id):
            """API: 获取训练session的检查点"""
            checkpoint_manager = CheckpointManager(session_id)
            checkpoints = checkpoint_manager.get_checkpoints()
            return jsonify(checkpoints)
        
        @self.app.route('/api/results')
        def api_results():
            """API: 获取所有训练结果"""
            results = self.get_all_training_results()
            return jsonify(results)
        
        @self.app.route('/api/result/<result_id>')
        def api_result(result_id):
            """API: 获取特定训练结果详情"""
            result_info = self.get_training_result_info(result_id)
            return jsonify(result_info)
        
        @self.app.route('/api/system_info')
        def api_system_info():
            """API: 获取系统信息"""
            return jsonify(self.get_system_info())
    
    def get_all_training_processes(self) -> List[Dict[str, Any]]:
        """获取所有训练进程信息"""
        processes = []
        
        # 获取正在运行的训练进程
        running_processes = self.training_monitor.get_running_processes()
        processes.extend(running_processes)
        
        # 获取已完成的训练进程
        completed_processes = self.get_completed_processes()
        processes.extend(completed_processes)
        
        return processes
    
    def get_training_process_info(self, process_id: str) -> Dict[str, Any]:
        """获取特定训练进程的详细信息"""
        # 检查是否正在运行
        running_info = self.training_monitor.get_process_info(process_id)
        if running_info:
            return running_info
        
        # 检查已完成的结果
        completed_info = self.get_completed_process_info(process_id)
        if completed_info:
            return completed_info
        
        return {"error": "Process not found"}
    
    def get_completed_processes(self) -> List[Dict[str, Any]]:
        """获取已完成的训练进程"""
        completed = []
        
        # 使用路径管理器获取所有session信息
        sessions = self.path_manager.list_sessions()
        
        for session in sessions:
            # 检查session是否有完成的训练结果
            session_info = self.get_completed_session_info(session["training_id"])
            if session_info:
                completed.append(session_info)
        
        return completed
    
    def get_completed_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取已完成session的信息"""
        session_dir = self.path_manager.get_user_data_root() / "logs" / session_id
        if not session_dir.exists():
            return None
        
        # 读取session信息
        info_file = session_dir / "session_info.json"
        status_file = session_dir / "status.json"
        
        info = {}
        
        # 尝试读取session信息
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
            except Exception as e:
                logger.error(f"Error reading session info for {session_id}: {e}")
        
        # 尝试读取状态信息
        if status_file.exists():
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_info = json.load(f)
                info.update(status_info)
            except Exception as e:
                logger.error(f"Error reading status info for {session_id}: {e}")
        
        if info:
            # 获取最新检查点
            checkpoint_manager = CheckpointManager(session_id)
            checkpoints = checkpoint_manager.get_checkpoints()
            latest_checkpoint = checkpoints[0] if checkpoints else None
            
            info.update({
                "session_id": session_id,
                "status": info.get("status", "completed"),
                "latest_checkpoint": latest_checkpoint,
                "checkpoint_count": len(checkpoints)
            })
            
            return info
        
        return None
    
    def get_all_training_results(self) -> List[Dict[str, Any]]:
        """获取所有训练结果"""
        results = []
        
        # 从报告目录获取结果
        reports_dir = Path("reports")
        if reports_dir.exists():
            for result_file in reports_dir.glob("*.json"):
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                    result["id"] = result_file.stem
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error reading result file {result_file}: {e}")
        
        return results
    
    def get_training_result_info(self, result_id: str) -> Dict[str, Any]:
        """获取特定训练结果的详细信息"""
        result_file = Path("reports") / f"{result_id}.json"
        if result_file.exists():
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                result["id"] = result_id
                return result
            except Exception as e:
                logger.error(f"Error reading result file {result_file}: {e}")
        
        return {"error": "Result not found"}
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "timestamp": time.time()
        }
    
    def start_monitor(self, host: str = "0.0.0.0", port: int = 5000):
        """启动监控服务"""
        logger.info(f"Starting monitor on {host}:{port}")
        self.app.run(host=host, port=port, debug=False)
    
    def start_background_monitor(self, host: str = "0.0.0.0", port: int = 5000):
        """在后台启动监控服务"""
        monitor_thread = threading.Thread(
            target=self.start_monitor,
            args=(host, port),
            daemon=True
        )
        monitor_thread.start()
        logger.info(f"Background monitor started on {host}:{port}")
        return monitor_thread


def main():
    """命令行入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="xAutoFactor Monitor")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=5000, help="Port to bind to")
    parser.add_argument("--background", action="store_true", help="Run in background")
    
    args = parser.parse_args()
    
    monitor = Monitor()
    
    if args.background:
        monitor.start_background_monitor(args.host, args.port)
        print(f"Monitor started in background on {args.host}:{args.port}")
        print("Press Ctrl+C to stop")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nMonitor stopped")
    else:
        monitor.start_monitor(args.host, args.port)


if __name__ == "__main__":
    main()
