"""
日志管理模块
使用loguru提供更好的日志功能，支持配置化日志级别和文件输出
"""

import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from loguru import logger


class Logger:
    """日志管理器"""

    _instance = None
    _configured = False
    _log_file = None

    def __new__(cls, name: str = __name__,level: str = "INFO"):
        """单例模式，确保只创建一个日志配置"""
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance.name = name
            cls._instance._setup_logging(level)
        return cls._instance

    @classmethod
    def reset(cls):
        """重置日志系统，允许重新配置"""
        if cls._instance:
            # 移除所有处理器
            logger.remove()
            cls._instance = None
            cls._configured = False
            cls._log_file = None

    def update_log_file(self, new_log_file: str, level: str = "INFO"):
        """更新日志文件路径"""
        if self._log_file != new_log_file:
            # 移除现有的文件处理器
            logger.remove()
            self._log_file = new_log_file
            self._configured = False
            self._setup_logging(level)

    def _setup_logging(self, level: str = "INFO"):
        """设置日志系统"""
        self._setup_logging_with_level(level)

    def debug(self, message: str):
        """记录调试信息"""
        logger.bind(name=self.name).debug(message)

    def info(self, message: str):
        """记录信息"""
        logger.bind(name=self.name).info(message)

    def warning(self, message: str):
        """记录警告"""
        logger.bind(name=self.name).warning(message)

    def error(self, message: str):
        """记录错误"""
        logger.bind(name=self.name).error(message)

    def critical(self, message: str):
        """记录严重错误"""
        logger.bind(name=self.name).critical(message)

    def exception(self, message: str):
        """记录异常"""
        logger.bind(name=self.name).exception(message)


    def _setup_logging_with_level(self, level: str = "INFO"):
        """设置日志系统，支持自定义级别"""
        if self._configured:
            return

        # 移除默认的处理器
        logger.remove()

        try:
            # 使用路径管理器获取日志目录（动态导入避免循环依赖）
            from .path_manager import get_path_manager

            path_manager = get_path_manager()

            # 尝试获取当前会话目录
            try:
                session_dir = path_manager.get_session_dir()
                # 如果成功获取会话目录，将日志文件保存到会话目录
                log_file = session_dir / "train.log"
                self._log_file = str(log_file)
            except ValueError:
                # 如果没有会话目录，使用默认的logs目录
                logs_dir = path_manager.get_user_data_root() / "logs"
                logs_dir.mkdir(exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d")
                log_file = logs_dir / f"auto_factor_{timestamp}.log"
                self._log_file = str(log_file)
        except Exception:
            # 如果路径管理器失败，使用默认目录
            if self._log_file is None:
                os.makedirs("logs", exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d")
                log_file = f"logs/auto_factor_{timestamp}.log"
                self._log_file = log_file

        # 文件输出格式
        file_format = (
            "{time:HH:mm:ss} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )

        # 添加控制台处理器，使用指定的级别
        logger.add(
            sys.stdout, level=level, colorize=True, backtrace=True, diagnose=True
        )

        # 添加文件处理器 - 只添加一个文件，包含所有级别的日志
        logger.add(
            self._log_file,
            format=file_format,
            level="DEBUG",  # 包含所有级别的日志，包括ERROR
            rotation="10 MB",  # 文件大小超过10MB时轮转
            retention="30 days",  # 保留30天的日志
            compression="zip",  # 压缩旧日志
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
        )

        self._configured = True


# 向后兼容的函数
def get_logger(name: str = __name__,level: str = "DEBUG") -> Logger:
    """获取logger实例（向后兼容）"""
    return Logger(name,level)

def reset_logging():
    """重置日志系统"""
    Logger.reset()
