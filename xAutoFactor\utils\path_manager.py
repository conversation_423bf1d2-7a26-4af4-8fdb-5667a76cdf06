#!/usr/bin/env python3
"""
路径管理模块
负责管理基于XAUTOFACTOR_USERPATH环境变量的用户数据目录
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List, Union
from loguru import logger


class PathManager:
    """路径管理器"""

    def __init__(self):
        # 延迟初始化logger，避免循环依赖
        self._logger = None
        self.user_data_path = self._get_user_data_path()
        self._ensure_directories()

        # 当前训练会话ID
        self.current_session_id = None
        self.get_current_session_id()

    def _get_user_data_path(self) -> Path:
        """获取用户数据路径"""
        # 检查环境变量
        user_path = os.environ.get("XAUTOFACTOR_USERPATH")

        if not user_path:
            raise ValueError(
                "环境变量 XAUTOFACTOR_USERPATH 未设置。\n"
                "请设置环境变量指向用户数据目录，例如：\n"
                "XAUTOFACTOR_USERPATH=C:\\demos\\auto_factor\\user_data"
            )

        user_data_path = Path(user_path)

        # 检查路径是否存在
        if not user_data_path.exists():
            # 避免在初始化时产生过多日志
            user_data_path.mkdir(parents=True, exist_ok=True)

        return user_data_path

    def _ensure_directories(self):
        """确保必要的子目录存在"""
        directories = ["config", "data_cache", "logs"]

        for directory in directories:
            dir_path = self.user_data_path / directory
            dir_path.mkdir(exist_ok=True)

    def create_training_session(self) -> str:
        """
        创建新的训练会话目录

        Returns:
            str: 训练会话ID
        """
        lock_file = self.get_lock_file()
        if lock_file.exists():
            raise ValueError("训练会话正在运行中")

        # 生成基于时间戳的会话ID
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建会话目录
        session_dir = self.user_data_path / "logs" / session_id
        session_dir.mkdir(parents=True, exist_ok=True)

        with open(lock_file, "w", encoding="utf-8") as f:
            f.write(session_id)

        self.current_session_id = session_id
        logger.info(f"创建训练会话: {session_id}, 目录: {session_dir}")

        return session_id

    def get_lock_file(self) -> Path:
        """获取锁文件路径"""
        return self.user_data_path / "logs" / "session.lock"

    def release_lock(self):
        """释放锁文件"""
        lock_file = self.get_lock_file()
        if lock_file.exists():
            lock_file.unlink()
            logger.info("锁文件已释放")
        else:
            logger.warning("锁文件不存在，无法释放")

    def get_current_session_id(self) -> Optional[str]:
        """获取当前训练会话ID"""
        if self.current_session_id is None:
            lock_file = self.get_lock_file()
            if lock_file.exists():
                with open(lock_file, "r", encoding="utf-8") as f:
                    self.current_session_id = f.read()
        return self.current_session_id

    def set_session_id(self, session_id: str):
        """
        设置当前会话ID（用于从指定session继续训练）

        Args:
            session_id: 要设置的会话ID
        """
        # 检查session目录是否存在
        session_dir = self.user_data_path / "logs" / session_id
        if not session_dir.exists():
            raise ValueError(f"Session目录不存在: {session_dir}")

        # 检查是否有其他会话正在运行
        lock_file = self.get_lock_file()
        if lock_file.exists():
            with open(lock_file, "r", encoding="utf-8") as f:
                existing_session = f.read().strip()
            if existing_session != session_id:
                raise ValueError(f"另一个训练会话正在运行中: {existing_session}")

        # 设置当前会话ID
        self.current_session_id = session_id

        # 创建或更新锁文件
        with open(lock_file, "w", encoding="utf-8") as f:
            f.write(session_id)

        logger.info(f"设置当前会话ID: {session_id}")

    def get_latest_checkpoint_path(self, session_id: str = None) -> Optional[Path]:
        """
        获取指定session的最新checkpoint路径

        Args:
            session_id: 会话ID，如果为None则使用当前会话

        Returns:
            Path: 最新checkpoint的路径，如果没有则返回None
        """
        if session_id is None:
            session_id = self.get_current_session_id()
            if session_id is None:
                return None

        session_dir = self.user_data_path / "logs" / session_id
        if not session_dir.exists():
            return None

        # 查找所有checkpoint文件
        checkpoint_files = list(session_dir.glob("ck_*.pkl"))
        if not checkpoint_files:
            return None

        # 按代数排序，找到最新的
        def extract_generation(path):
            try:
                filename = path.name
                if filename.startswith("ck_") and filename.endswith(".pkl"):
                    generation_str = filename.replace("ck_", "").replace(".pkl", "")
                    return int(generation_str)
                return 0
            except:
                return 0

        checkpoint_files.sort(key=extract_generation, reverse=True)
        return checkpoint_files[0]

    def get_session_dir(self) -> Path:
        """
        获取训练会话目录

        Args:
            session_id (str): 会话ID，如果为None则使用当前会话

        Returns:
            Path: 会话目录路径
        """
        if self.current_session_id is None:
            raise ValueError("未设置训练会话ID，请先调用create_training_session()")

        return self.user_data_path / "logs" / self.current_session_id

    def get_config_path(self, config_name: str) -> Path:
        """获取配置文件路径"""
        # 如果config_name已经包含.json扩展名，直接使用
        if not config_name.endswith(".json"):
            config_name += ".json"

        if config_name.endswith(".json"):
            return self.user_data_path / "config" / config_name

    def get_checkpoint_path(
        self, checkpoint_name: str = None, generation: int = None
    ) -> Path:
        """
        获取检查点路径

        Args:
            checkpoint_name (str): 检查点文件名，如果为None则生成基于代数的文件名
            generation (int): 代数，用于生成文件名

        Returns:
            Path: 检查点文件路径
        """
        if checkpoint_name:
            return self.get_session_dir() / checkpoint_name
        elif generation is not None:
            return self.get_session_dir() / f"ck_{generation}.pkl"
        else:
            raise ValueError("必须提供checkpoint_name或generation参数")

    def get_data_cache_path(self, cache_name: str) -> Path:
        """获取数据缓存路径"""
        return self.user_data_path / "data_cache" / cache_name

    def get_log_path(self, log_name: str = None) -> Path:
        """
        获取日志路径

        Args:
            log_name (str): 日志文件名，如果为None则使用默认名称

        Returns:
            Path: 日志文件路径
        """
        if log_name is None:
            log_name = "train.log"
            return self.get_session_dir() / log_name
        else:
            # 兼容旧版本，返回logs目录下的路径
            return self.user_data_path / "logs" / log_name

    def get_report_path(self) -> Path:
        """
        获取报告路径

        Args:
            report_name (str): 报告文件名，如果为None则使用默认名称

        Returns:
            Path: 报告文件路径
        """
        return self.get_session_dir() / "report.html"

    def get_user_data_root(self) -> Path:
        """获取用户数据根目录"""
        return self.user_data_path

    def list_configs(self) -> list:
        """列出所有可用的配置文件"""
        config_dir = self.user_data_path / "config"
        if not config_dir.exists():
            return []

        configs = []
        for config_file in config_dir.glob("*.json"):
            configs.append(config_file.stem)  # 不带扩展名的文件名

        return sorted(configs)

    def config_exists(self, config_name: str) -> bool:
        """检查配置文件是否存在"""
        config_path = self.get_config_path(config_name)
        return config_path.exists()

    def get_train_script_path(self) -> Path:
        """获取训练脚本路径"""
        return self.get_session_dir() / "train_script.py"

    def get_status_path(self) -> Path:
        """获取状态文件路径"""
        return self.get_session_dir() / "status.json"

    def get_status_dict(self) -> dict:
        status_file = self.get_status_path()

        # 读取当前状态
        if status_file.exists():
            with open(status_file, "r", encoding="utf-8") as f:
                current_status = json.load(f)
        else:
            current_status = {}
        return current_status

    def save_train_script(self, script_content: str):
        """保存训练脚本"""
        script_path = self.get_train_script_path()
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(script_content)
        logger.info(f"训练脚本已保存: {script_path}")

    def save_status(self, status_data: Dict):
        """保存状态信息"""
        status_path = self.get_status_path()
        with open(status_path, "w", encoding="utf-8") as f:
            json.dump(status_data, f, indent=2, ensure_ascii=False)

    def save_report(self, report_content: str):
        """保存报告"""
        report_path = self.get_report_path()
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report_content)
        logger.info(f"报告已保存: {report_path}")

    def save_checkpoint(self, generation: int, data: dict) -> Path:
        """
        保存检查点数据到当前session目录

        Args:
            generation: 代数
            data: 要保存的数据

        Returns:
            Path: 保存的检查点文件路径
        """
        import pickle

        checkpoint_path = self.get_checkpoint_path(generation=generation)

        try:
            # 确保session目录存在
            checkpoint_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存数据
            with open(checkpoint_path, "wb") as f:
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)

            logger.info(f"检查点已保存: {checkpoint_path}")
            return checkpoint_path

        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
            raise

    def load_checkpoint(
        self, checkpoint_path: Optional[Union[Path, str]] = None, generation: int = None
    ) -> dict:
        """
        加载检查点数据

        Args:
            checkpoint_path: 检查点文件路径，如果为None则使用generation参数
            generation: 代数，用于构造文件路径

        Returns:
            dict: 加载的数据字典
        """
        import pickle

        if checkpoint_path and type(checkpoint_path) == str:
            checkpoint_path = Path(checkpoint_path)

        if checkpoint_path is None:
            if generation is not None:
                checkpoint_path = self.get_checkpoint_path(generation=generation)
            else:
                raise ValueError("必须提供checkpoint_path或generation参数")

        try:
            with open(checkpoint_path, "rb") as f:
                data = pickle.load(f)

            logger.info(f"检查点已加载: {checkpoint_path}")
            return data

        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            raise

    def list_checkpoints(self) -> list:
        """
        列出当前session的所有检查点文件

        Returns:
            list: 检查点文件信息列表
        """
        checkpoints = []
        session_dir = self.get_session_dir()

        for checkpoint_file in session_dir.glob("ck_*.pkl"):
            try:
                stat = checkpoint_file.stat()
                # 从文件名解析代数
                filename = checkpoint_file.name
                if filename.startswith("ck_") and filename.endswith(".pkl"):
                    generation_str = filename.replace("ck_", "").replace(".pkl", "")
                    generation = int(generation_str)
                else:
                    generation = 0

                checkpoints.append(
                    {
                        "filename": checkpoint_file.name,
                        "path": str(checkpoint_file),
                        "generation": generation,
                        "size_kb": round(stat.st_size / 1024, 1),
                        "timestamp": stat.st_mtime,
                        "datetime": datetime.fromtimestamp(stat.st_mtime).strftime(
                            "%Y/%m/%d %H:%M:%S"
                        ),
                    }
                )
            except Exception as e:
                logger.error(f"读取检查点文件信息失败 {checkpoint_file}: {e}")

        # 按代数排序
        checkpoints.sort(key=lambda x: x["generation"], reverse=True)
        return checkpoints

    def cleanup_old_checkpoints(self, keep_count: int = 5) -> int:
        """
        清理旧的检查点文件，只保留最新的几个

        Args:
            keep_count: 保留的检查点数量

        Returns:
            int: 删除的文件数量
        """
        session_dir = self.get_session_dir()
        checkpoint_files = list(session_dir.glob("ck_*.pkl"))

        # 按修改时间排序，最新的在前
        checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

        deleted_count = 0
        for checkpoint_file in checkpoint_files[keep_count:]:
            try:
                checkpoint_file.unlink()
                deleted_count += 1
                logger.info(f"删除旧检查点: {checkpoint_file}")
            except Exception as e:
                logger.error(f"删除检查点失败 {checkpoint_file}: {e}")

        return deleted_count

    def list_sessions(self) -> List[Dict]:
        """列出所有训练会话"""
        logs_dir = self.user_data_path / "logs"
        sessions = []

        if logs_dir.exists():
            for session_dir in logs_dir.iterdir():
                if session_dir.is_dir():
                    try:
                        session_info = self._get_session_info(session_dir)
                        sessions.append(session_info)
                    except Exception as e:
                        logger.warning(f"无法读取会话信息 {session_dir.name}: {e}")

        # 按创建时间排序（最新的在前）
        sessions.sort(key=lambda x: x["created_time"], reverse=True)
        return sessions

    def _get_session_info(self, session_dir: Path) -> Dict:
        """获取会话信息"""
        info = {
            "training_id": session_dir.name,
            "session_dir": str(session_dir),
            "created_time": datetime.fromtimestamp(
                session_dir.stat().st_ctime
            ).isoformat(),
            "files": {},
        }

        # 检查文件是否存在
        files_to_check = {
            "train_script": session_dir / "train_script.py",
            "status": session_dir / "status.json",
            "report": session_dir / "report.html",
            "log": session_dir / "train.log",
        }

        for name, path in files_to_check.items():
            if path.exists():
                info["files"][name] = {
                    "exists": True,
                    "size": path.stat().st_size,
                    "modified": datetime.fromtimestamp(
                        path.stat().st_mtime
                    ).isoformat(),
                }
            else:
                info["files"][name] = {"exists": False}

        # 检查checkpoint文件
        checkpoints = []
        for file in session_dir.glob("ck_*.pkl"):
            checkpoints.append(
                {
                    "filename": file.name,
                    "size": file.stat().st_size,
                    "modified": datetime.fromtimestamp(
                        file.stat().st_mtime
                    ).isoformat(),
                }
            )

        info["files"]["checkpoints"] = checkpoints
        info["checkpoint_count"] = len(checkpoints)

        return info


# 全局路径管理器实例
_path_manager: Optional[PathManager] = None


def get_path_manager() -> PathManager:
    """获取全局路径管理器实例"""
    global _path_manager
    if _path_manager is None:
        _path_manager = PathManager()
    return _path_manager
