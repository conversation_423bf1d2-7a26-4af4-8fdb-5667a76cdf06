import pickle
import sys

# 检查最新的种群数据
data = pickle.load(open("user_data/logs/20250818_162854/ck_16.pkl", "rb"))
print("Population size:", len(data["population"]))
print("\nSample DNA:")
for i, ind in enumerate(data["population"][:10]):
    print(f"{i+1}: {ind}")

# 检查重复DNA
dna_set = set()
duplicates = 0
for ind in data["population"]:
    dna_str = str(ind)
    if dna_str in dna_set:
        duplicates += 1
    else:
        dna_set.add(dna_str)

print(f'\nTotal individuals: {len(data["population"])}')
print(f"Unique individuals: {len(dna_set)}")
print(f"Duplicates: {duplicates}")
print(f'Duplicate rate: {duplicates/len(data["population"])*100:.1f}%')

# 分析DNA复杂度
print(f"\nDNA复杂度分析:")
complexity_stats = {}
for ind in data["population"]:
    buy_signal, sell_signal = str(ind).split("|")

    # 计算买入信号复杂度（and/or操作符数量）
    buy_complexity = buy_signal.count(" and ") + buy_signal.count(" or ") + 1
    sell_complexity = sell_signal.count(" and ") + sell_signal.count(" or ") + 1

    total_complexity = buy_complexity + sell_complexity

    if total_complexity not in complexity_stats:
        complexity_stats[total_complexity] = 0
    complexity_stats[total_complexity] += 1

print("复杂度分布:")
for complexity in sorted(complexity_stats.keys()):
    count = complexity_stats[complexity]
    percentage = count / len(data["population"]) * 100
    print(f"  复杂度 {complexity}: {count} 个 ({percentage:.1f}%)")
