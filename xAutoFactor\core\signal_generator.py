# signal_generator.py - 信号生成器模块
"""
基于预定义布尔指标生成交易信号表达式
使用逻辑运算符组合布尔指标，生成有意义的交易信号
"""

import pandas as pd
from loguru import logger
from .data_manager import DataManager
from typing import List, Dict, Tuple, Any
import random
import re
import warnings

warnings.filterwarnings("ignore")


class SignalGenerator:
    """交易信号生成器，基于布尔指标组合"""

    def __init__(self, config: Dict):
        """
        初始化信号生成器

        Args:
            config (dict): 信号配置参数
        """
        self.config = config
        self.max_depth = config.get("max_depth", 3)  # 降低复杂度
        self.max_components = config.get("max_components", 4)  # 最大组件数

        # 渐进式复杂度控制
        self.progressive_complexity = config.get("progressive_complexity", True)
        self.current_generation = 0
        self.max_generation = config.get("max_generation", 50)

        # 多样性控制
        self.diversity_control = config.get("diversity_control", True)
        self.generated_expressions = set()  # 用于跟踪已生成的表达式
        self.max_retry_attempts = config.get("max_retry_attempts", 50)

        # 从DataManager获取布尔指标定义
        data_manager = DataManager(config)
        self.boolean_indicators = data_manager.get_boolean_indicators()

        # 逻辑操作符
        self.logical_ops = ["and", "or"]

        # 缓存数据，避免重复计算
        self.data_cache = {}

        logger.info(f"信号生成器初始化完成，指标数量: {len(self.boolean_indicators)}")
        logger.debug(f"可用指标: {self.boolean_indicators[:10]}...")  # 只显示前10个

    def set_current_generation(self, generation: int):
        """设置当前代数，用于渐进式复杂度控制"""
        self.current_generation = generation

    def get_max_complexity_for_generation(self) -> int:
        """根据当前代数计算允许的最大复杂度"""
        if not self.progressive_complexity:
            return self.max_components

        # 渐进式增加复杂度：前几代只允许简单信号
        if self.current_generation <= 2:
            return 1  # 只允许单个指标
        elif self.current_generation <= 5:
            return 2  # 允许2个指标组合
        elif self.current_generation <= 10:
            return 3  # 允许3个指标组合
        else:
            return self.max_components  # 允许最大复杂度

    def is_expression_unique(self, expression: str) -> bool:
        """检查表达式是否唯一"""
        if not self.diversity_control:
            return True
        return expression not in self.generated_expressions

    def add_expression_to_cache(self, expression: str):
        """将表达式添加到缓存中"""
        if self.diversity_control:
            self.generated_expressions.add(expression)

    def generate_simple_signal(self, signal_type: str = "buy") -> str:
        """
        生成简单的信号表达式（单个布尔指标）

        Args:
            signal_type (str): 'buy' 或 'sell'

        Returns:
            str: 简单信号表达式
        """
        try:
            # 从所有布尔指标中随机选择
            indicator = random.choice(self.boolean_indicators)

            # 随机决定是否取反
            if random.random() < 0.1:  # 10%的概率取反
                return f"not {indicator}"
            else:
                return indicator

        except Exception as e:
            logger.error(f"生成简单信号失败: {e}")
            return "rsi_oversold" if signal_type == "buy" else "rsi_overbought"

    def generate_complex_signal(
        self, signal_type: str = "buy", max_components: int = None
    ) -> str:
        """
        生成复杂的信号表达式（多个布尔指标用逻辑操作符连接）

        Args:
            signal_type (str): 'buy' 或 'sell'
            max_components (int): 最大组件数量

        Returns:
            str: 复杂信号表达式
        """
        try:
            if max_components is None:
                max_components = self.max_components

            num_components = random.randint(2, max_components)
            components = []

            # 使用所有布尔指标作为候选池
            indicator_pool = self.boolean_indicators.copy()

            # 生成多个组件
            used_indicators = set()
            for _ in range(num_components):
                # 避免重复使用相同指标
                available_indicators = [
                    ind for ind in indicator_pool if ind not in used_indicators
                ]
                if not available_indicators:
                    available_indicators = indicator_pool

                indicator = random.choice(available_indicators)
                used_indicators.add(indicator)

                # 随机决定是否取反（概率较低）
                if random.random() < 0.15:  # 15%的概率取反
                    components.append(f"not {indicator}")
                else:
                    components.append(indicator)

            # 用逻辑操作符连接
            result = components[0]
            for i in range(1, len(components)):
                logical_op = random.choice(self.logical_ops)

                # 对于买入信号，更倾向于使用 'and'（条件要求更严格）
                # 对于卖出信号，更平衡地使用 'and' 和 'or'
                if signal_type == "buy" and random.random() < 0.7:
                    logical_op = "and"

                result = f"({result}) {logical_op} ({components[i]})"

            return result

        except Exception as e:
            logger.error(f"生成复杂信号失败: {e}")
            return self.generate_simple_signal(signal_type)

    def generate_individual(self) -> Tuple[str, str]:
        """
        生成一个包含买入和卖出信号的个体

        Returns:
            tuple: (买入信号表达式, 卖出信号表达式)
        """
        try:
            # 决定生成简单还是复杂信号
            complexity_prob = random.random()

            if complexity_prob < 0.3:  # 30%概率生成简单信号
                buy_expr = self.generate_simple_signal("buy")
                sell_expr = self.generate_simple_signal("sell")
            elif complexity_prob < 0.8:  # 50%概率生成中等复杂信号
                max_comp = random.randint(2, 3)
                buy_expr = self.generate_complex_signal("buy", max_comp)
                sell_expr = self.generate_complex_signal("sell", max_comp)
            else:  # 20%概率生成复杂信号
                max_comp = random.randint(3, self.max_components)
                buy_expr = self.generate_complex_signal("buy", max_comp)
                sell_expr = self.generate_complex_signal("sell", max_comp)

            return (buy_expr, sell_expr)

        except Exception as e:
            logger.error(f"生成个体失败: {e}")
            return ("rsi_oversold and ma5_gt_ma20", "rsi_overbought or ma_bearish")

    def generate_signals(
        self, data: pd.DataFrame, buy_expr: str, sell_expr: str
    ) -> pd.Series:
        """
        根据表达式生成交易信号

        Args:
            data (pd.DataFrame): 包含布尔指标的数据
            buy_expr (str): 买入信号表达式
            sell_expr (str): 卖出信号表达式

        Returns:
            pd.Series: 交易信号 (1=买入, -1=卖出, 0=持有)
        """
        try:
            # 初始化信号序列
            signals = pd.Series(0, index=data.index)

            # 逐行评估信号
            for i in range(len(data)):
                try:
                    # 获取当前行数据
                    current_row = data.iloc[i]

                    # 评估买入条件
                    buy_condition = self._evaluate_boolean_expression(
                        buy_expr, current_row
                    )

                    # 评估卖出条件
                    sell_condition = self._evaluate_boolean_expression(
                        sell_expr, current_row
                    )

                    # 生成信号（买入优先，避免同时买卖）
                    if buy_condition and not sell_condition:
                        signals.iloc[i] = 1  # 买入信号
                    elif sell_condition and not buy_condition:
                        signals.iloc[i] = -1  # 卖出信号
                    else:
                        signals.iloc[i] = 0  # 持有或无信号

                except Exception as e:
                    logger.debug(f"评估第{i}行信号时出错: {e}")
                    signals.iloc[i] = 0

            return signals

        except Exception as e:
            logger.error(f"生成信号失败: {e}")
            return pd.Series(0, index=data.index)

    def _evaluate_boolean_expression(self, expr: str, row_data: pd.Series) -> bool:
        """
        安全地评估布尔表达式

        Args:
            expr (str): 布尔表达式
            row_data (pd.Series): 当前行数据

        Returns:
            bool: 表达式结果
        """
        try:
            # 创建评估环境
            eval_env = {}

            # 添加所有布尔指标的值
            for indicator in self.boolean_indicators:
                if indicator in row_data:
                    eval_env[indicator] = bool(row_data[indicator])
                else:
                    eval_env[indicator] = False

            # 添加安全的逻辑操作符
            eval_env.update(
                {
                    "and": lambda x, y: bool(x) and bool(y),
                    "or": lambda x, y: bool(x) or bool(y),
                    "not": lambda x: not bool(x),
                    "True": True,
                    "False": False,
                    "__builtins__": {},
                }
            )

            # 预处理表达式，将逻辑操作符转换为函数调用（如果需要）
            processed_expr = self._preprocess_expression(expr)

            # 安全执行表达式
            result = eval(processed_expr, eval_env)
            return bool(result)

        except Exception as e:
            logger.debug(f"布尔表达式求值失败 '{expr}': {e}")
            return False

    def _preprocess_expression(self, expr: str) -> str:
        """
        预处理表达式，确保逻辑操作符正确

        Args:
            expr (str): 原始表达式

        Returns:
            str: 处理后的表达式
        """
        try:
            # 移除多余的空格
            processed = re.sub(r"\s+", " ", expr.strip())

            # 确保 not 操作符正确
            processed = re.sub(r"\bnot\s+", "not ", processed)

            return processed

        except Exception as e:
            logger.debug(f"预处理表达式失败: {e}")
            return expr

    def validate_signal_expression(
        self, expr: str, data: pd.DataFrame, max_test_rows: int = 100
    ) -> bool:
        """
        验证信号表达式是否有效

        Args:
            expr (str): 信号表达式
            data (pd.DataFrame): 测试数据
            max_test_rows (int): 最大测试行数

        Returns:
            bool: 表达式是否有效
        """
        try:
            # 检查表达式是否包含有效的指标
            used_indicators = [ind for ind in self.boolean_indicators if ind in expr]
            if not used_indicators:
                return False

            # 检查使用的指标是否在数据中存在
            missing_indicators = [
                ind for ind in used_indicators if ind not in data.columns
            ]
            if missing_indicators:
                logger.warning(f"表达式中使用了不存在的指标: {missing_indicators}")
                return False

            # 测试表达式在部分数据上的执行
            test_rows = min(max_test_rows, len(data))
            valid_results = 0

            for i in range(test_rows):
                try:
                    result = self._evaluate_boolean_expression(expr, data.iloc[i])
                    if isinstance(result, bool):
                        valid_results += 1
                except:
                    continue

            # 如果至少80%的结果有效，认为表达式有效
            return (valid_results / test_rows) >= 0.8

        except Exception as e:
            logger.error(f"验证表达式失败: {e}")
            return False

    def individual_to_expressions(self, individual: Tuple[Any, Any]) -> Tuple[str, str]:
        """
        将个体转换为人类可读的表达式

        Args:
            individual: (买入表达式, 卖出表达式)

        Returns:
            tuple: (买入信号字符串, 卖出信号字符串)
        """
        try:
            # 如果individual已经是字符串元组，直接返回
            if isinstance(individual[0], str) and isinstance(individual[1], str):
                return individual

            # 如果是其他格式，尝试转换
            buy_str = str(individual[0])
            sell_str = str(individual[1])

            return buy_str, sell_str

        except Exception as e:
            logger.error(f"转换表达式失败: {e}")
            return ("rsi_oversold", "rsi_overbought")

    def simplify_expression(self, expr: str) -> str:
        """
        简化表达式

        Args:
            expr (str): 原始表达式

        Returns:
            str: 简化后的表达式
        """
        try:
            simplified = expr.strip()

            # 移除多余的空格
            simplified = re.sub(r"\s+", " ", simplified)

            # 移除多余的括号（简单情况）
            simplified = re.sub(r"\(\s*([a-zA-Z_]\w*)\s*\)", r"\1", simplified)

            # 简化双重否定
            simplified = re.sub(r"not\s+not\s+", "", simplified)

            return simplified

        except Exception as e:
            logger.error(f"简化表达式失败: {e}")
            return expr

    def get_expression_complexity(self, individual: Tuple[str, str]) -> int:
        """
        计算表达式的复杂度

        Args:
            individual: 信号表达式对

        Returns:
            int: 复杂度分数
        """
        try:
            buy_expr, sell_expr = individual

            # 计算指标数量
            buy_indicators = len(
                [ind for ind in self.boolean_indicators if ind in buy_expr]
            )
            sell_indicators = len(
                [ind for ind in self.boolean_indicators if ind in sell_expr]
            )

            # 计算逻辑操作符数量
            buy_operators = len(re.findall(r"\b(and|or|not)\b", buy_expr))
            sell_operators = len(re.findall(r"\b(and|or|not)\b", sell_expr))

            # 复杂度 = 指标数 + 操作符数
            return buy_indicators + sell_indicators + buy_operators + sell_operators

        except Exception as e:
            logger.error(f"计算复杂度失败: {e}")
            return 0

    def generate_targeted_signal(
        self, signal_type: str, categories: List[str] = None
    ) -> str:
        """
        生成针对特定类别的信号

        Args:
            signal_type (str): 'buy' 或 'sell'
            categories (List[str]): 指标类别列表，如 ['trend', 'momentum', 'volume']

        Returns:
            str: 目标信号表达式
        """
        try:
            # 定义指标类别
            category_indicators = {
                "trend": [
                    "ma5_gt_ma10",
                    "ma5_gt_ma20",
                    "ma10_gt_ma20",
                    "ma_bullish",
                    "ma_bearish",
                    "close_gt_ma5",
                    "close_gt_ma20",
                ],
                "momentum": [
                    "rsi_oversold",
                    "rsi_overbought",
                    "rsi_rising",
                    "rsi_falling",
                    "macd_bullish",
                    "macd_bearish",
                    "macd_golden_cross",
                    "macd_death_cross",
                ],
                "volume": ["volume_surge", "volume_dry", "volume_above_avg"],
                "volatility": [
                    "bb_squeeze",
                    "bb_expansion",
                    "high_volatility",
                    "low_volatility",
                ],
                "pattern": [
                    "price_rising",
                    "price_falling",
                    "consecutive_up_2",
                    "consecutive_down_2",
                ],
            }

            # 如果没有指定类别，随机选择
            if categories is None:
                categories = random.sample(
                    list(category_indicators.keys()), random.randint(1, 3)
                )

            # 从指定类别中收集指标
            candidate_indicators = []
            for category in categories:
                if category in category_indicators:
                    candidate_indicators.extend(category_indicators[category])

            if not candidate_indicators:
                # 如果没有找到指标，使用默认方法
                return self.generate_simple_signal(signal_type)

            # 过滤掉不适合信号类型的指标
            if signal_type == "buy":
                suitable_indicators = [
                    ind
                    for ind in candidate_indicators
                    if ind in self.boolean_indicators or ind in candidate_indicators
                ]
            else:
                suitable_indicators = [
                    ind
                    for ind in candidate_indicators
                    if ind in self.boolean_indicators or ind in candidate_indicators
                ]

            if not suitable_indicators:
                suitable_indicators = candidate_indicators

            # 生成表达式
            num_components = random.randint(1, min(3, len(suitable_indicators)))
            selected_indicators = random.sample(suitable_indicators, num_components)

            if num_components == 1:
                return selected_indicators[0]
            else:
                # 用逻辑操作符连接
                result = selected_indicators[0]
                for i in range(1, len(selected_indicators)):
                    logical_op = random.choice(["and", "or"])
                    result = f"({result}) {logical_op} ({selected_indicators[i]})"
                return result

        except Exception as e:
            logger.error(f"生成目标信号失败: {e}")
            return self.generate_simple_signal(signal_type)

    def get_signal_statistics(self, signals: pd.Series) -> Dict:
        """
        获取信号统计信息

        Args:
            signals (pd.Series): 信号序列

        Returns:
            dict: 统计信息
        """
        try:
            total_signals = len(signals)
            buy_signals = (signals == 1).sum()
            sell_signals = (signals == -1).sum()
            hold_signals = (signals == 0).sum()

            return {
                "total_signals": total_signals,
                "buy_signals": int(buy_signals),
                "sell_signals": int(sell_signals),
                "hold_signals": int(hold_signals),
                "buy_ratio": (
                    float(buy_signals / total_signals) if total_signals > 0 else 0.0
                ),
                "sell_ratio": (
                    float(sell_signals / total_signals) if total_signals > 0 else 0.0
                ),
                "activity_ratio": (
                    float((buy_signals + sell_signals) / total_signals)
                    if total_signals > 0
                    else 0.0
                ),
            }

        except Exception as e:
            logger.error(f"计算信号统计失败: {e}")
            return {}

    def test_signal_generation(
        self, data: pd.DataFrame, num_tests: int = 10
    ) -> List[Dict]:
        """
        测试信号生成功能

        Args:
            data (pd.DataFrame): 测试数据
            num_tests (int): 测试次数

        Returns:
            list: 测试结果列表
        """
        results = []

        for i in range(num_tests):
            try:
                # 生成信号对
                buy_expr, sell_expr = self.generate_individual()

                # 验证表达式
                buy_valid = self.validate_signal_expression(buy_expr, data)
                sell_valid = self.validate_signal_expression(sell_expr, data)

                # 如果都有效，生成信号并统计
                statistics = {}
                if buy_valid and sell_valid:
                    try:
                        signals = self.generate_signals(data, buy_expr, sell_expr)
                        statistics = self.get_signal_statistics(signals)
                    except:
                        statistics = {"error": "signal_generation_failed"}

                result = {
                    "test_id": i + 1,
                    "buy_expression": buy_expr,
                    "sell_expression": sell_expr,
                    "buy_valid": buy_valid,
                    "sell_valid": sell_valid,
                    "complexity": self.get_expression_complexity((buy_expr, sell_expr)),
                    "statistics": statistics,
                }

                results.append(result)

                logger.info(
                    f"测试 {i+1}: 买入='{buy_expr}' ({buy_valid}) | "
                    f"卖出='{sell_expr}' ({sell_valid}) | 复杂度={result['complexity']}"
                )

            except Exception as e:
                logger.error(f"测试 {i+1} 失败: {e}")
                results.append(
                    {
                        "test_id": i + 1,
                        "buy_expression": "Error",
                        "sell_expression": "Error",
                        "buy_valid": False,
                        "sell_valid": False,
                        "complexity": 0,
                        "statistics": {"error": str(e)},
                    }
                )

        return results
