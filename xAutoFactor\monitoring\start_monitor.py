#!/usr/bin/env python3
"""
训练监控启动脚本
简化启动训练监控服务器的过程
"""

import sys
import os
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    parser = argparse.ArgumentParser(description='启动AutoFactor训练监控服务器')
    parser.add_argument('--host', default='localhost', help='服务器主机地址 (默认: localhost)')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口 (默认: 5000)')
    parser.add_argument('--no-browser', action='store_true', help='不自动打开浏览器')
    
    args = parser.parse_args()
    
    try:
        from training_monitor import start_monitor_server
        
        print("🚀 正在启动AutoFactor训练监控服务器...")
        print(f"📊 监控地址: http://{args.host}:{args.port}")
        
        if not args.no_browser:
            print("🌐 浏览器将自动打开监控页面")
        else:
            print("🌐 请手动在浏览器中打开监控页面")
        
        print("\n📋 使用说明:")
        print("1. 在监控页面点击'开始训练'按钮启动训练")
        print("2. 实时查看训练进度、适应度变化和日志")
        print("3. 可以随时停止训练")
        print("4. 按 Ctrl+C 停止监控服务器")
        
        print("\n" + "="*50)
        
        # 启动监控服务器
        start_monitor_server(
            host=args.host, 
            port=args.port, 
            auto_open=not args.no_browser
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 监控服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
