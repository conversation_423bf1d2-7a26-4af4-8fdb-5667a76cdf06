Metadata-Version: 2.4
Name: xAutoFactor
Version: 1.0.0
Summary: 基于遗传编程的交易信号优化系统
Home-page: https://github.com/xautofactor/xautofactor
Author: xAutoFactor Team
Author-email: <EMAIL>
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Office/Business :: Financial :: Investment
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy
Requires-Dist: pandas
Requires-Dist: scipy
Requires-Dist: yfinance
Requires-Dist: TA-Lib
Requires-Dist: deap
Requires-Dist: matplotlib
Requires-Dist: seaborn
Requires-Dist: plotly
Requires-Dist: tqdm
Requires-Dist: psutil
Requires-Dist: loguru
Requires-Dist: flask
Requires-Dist: python-dateutil
Provides-Extra: dev
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytest-cov>=2.0; extra == "dev"
Requires-Dist: black>=21.0; extra == "dev"
Requires-Dist: flake8>=3.8; extra == "dev"
Requires-Dist: mypy>=0.800; extra == "dev"
Provides-Extra: monitoring
Requires-Dist: flask>=2.0; extra == "monitoring"
Requires-Dist: psutil>=5.8; extra == "monitoring"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# xAutoFactor - 自动因子发现框架

基于遗传编程的交易信号优化系统，提供完整的因子发现、策略回测和监控功能。

## 新版本特性

### 🏗️ 模块化重构
- **核心模块** (`xAutoFactor.core`): 数据处理、评估、信号生成和遗传编程优化
- **训练模块** (`xAutoFactor.training`): 训练脚本和进程管理
- **监控模块** (`xAutoFactor.monitoring`): 训练监控和检查点管理
- **工具模块** (`xAutoFactor.utils`): 日志、报告和环境设置
- **命令行工具** (`xAutoFactor.cli`): 统一的命令行接口

### 🚀 命令行工具
安装后可直接使用以下命令：

```bash
# 启动监控界面
auto_factor monitor

# 快速开始训练
auto_factor train quick_start

# 使用配置文件训练
auto_factor train config/train.json

# 测试自定义信号
auto_factor custom "close > ma(close, 20)" "close < ma(close, 10)" 000001.SZ
```

### 📊 统一监控系统
- 实时训练进度监控
- 历史结果查看
- 系统资源监控
- 检查点管理

## 安装

```bash
pip install -e .
```

## 环境配置

### 设置环境变量
在使用前，需要设置环境变量 `XAUTOFACTOR_USERPATH` 指向用户数据目录：

```bash
# Windows PowerShell
$env:XAUTOFACTOR_USERPATH="C:\demos\auto_factor\user_data"

# Windows CMD
set XAUTOFACTOR_USERPATH=C:\demos\auto_factor\user_data

# Linux/Mac
export XAUTOFACTOR_USERPATH=/path/to/your/user_data
```

### 目录结构
用户数据目录将包含以下子目录：
- `config/` - 配置文件
- `checkpoints/` - 训练检查点
- `data_cache/` - 数据缓存
- `logs/` - 日志文件
- `reports/` - 训练报告

## 快速开始

### 1. 启动监控界面
```bash
auto_factor monitor
```
访问 http://localhost:5000 查看监控界面

### 2. 快速训练
```bash
auto_factor train quick_start
```

### 3. 使用配置文件训练
```bash
auto_factor train train_config
```

### 4. 测试自定义策略
```bash
auto_factor custom "rsi_oversold" "rsi_overbought" 000001.SZ
```

## 项目结构

```
xAutoFactor/
├── core/                    # 核心功能模块
│   ├── data_manager.py     # 数据管理
│   ├── evaluator.py        # 策略评估
│   ├── signal_generator.py # 信号生成
│   ├── gp_optimizer.py     # 遗传编程优化
│   ├── trading_signal_gp_framework.py # 主框架
│   └── strategy_tester.py  # 策略测试
├── training/               # 训练模块
│   ├── training_script.py  # 训练脚本
│   ├── training_process_manager.py # 进程管理
│   └── quick_start.py      # 快速开始
├── monitoring/             # 监控模块
│   ├── monitor.py          # 主监控器
│   ├── checkpoint_manager.py # 检查点管理
│   ├── training_monitor.py # 训练监控
│   ├── independent_monitor.py # 独立监控
│   └── templates/          # 监控模板
├── utils/                  # 工具模块
│   ├── logger.py           # 日志管理
│   ├── reporter.py         # 报告生成
│   ├── environment_setup.py # 环境设置
│   └── utils.py            # 通用工具
└── cli/                    # 命令行工具
    └── main.py             # 主命令行接口
```

## 配置

配置文件位于用户数据目录的 `config/` 子目录中：

- `quick_start_config.json`: 快速开始配置（小规模测试）
- `train_config.json`: 完整训练配置

系统会自动创建这些配置文件（如果不存在）。

## 功能特性

### 🔍 因子发现
- 基于遗传编程的自动因子发现
- 支持多种技术指标组合
- 可配置的适应度函数

### 📈 策略回测
- 完整的回测框架
- 多种性能指标
- 可视化分析

### 🎯 监控系统
- 实时训练进度
- 系统资源监控
- 检查点恢复

### 🛠️ 开发工具
- 统一的命令行接口
- 模块化设计
- 完整的日志系统

## 开发

### 添加新功能
1. 在对应模块中添加功能
2. 更新 `__init__.py` 文件
3. 添加命令行接口（如需要）
4. 更新文档

### 测试
```bash
# 测试监控功能
auto_factor monitor

# 测试训练功能
auto_factor train quick_start

# 测试策略回测
auto_factor custom "close > 100" "close < 90" 000001.SZ
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
