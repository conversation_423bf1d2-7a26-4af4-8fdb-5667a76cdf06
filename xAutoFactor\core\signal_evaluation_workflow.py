"""
信号评估工作流
在遗传算法训练前评估所有信号的有效性
"""

import os
from pathlib import Path
from typing import Dict, List, Optional
import logging

from ..utils.logger import get_logger
from ..utils.path_manager import get_path_manager
from .signal_effectiveness_evaluator import SignalEffectivenessEvaluator

logger = get_logger(__name__)


class SignalEvaluationWorkflow:
    """信号评估工作流管理器"""
    
    def __init__(self, config: Dict):
        """
        初始化工作流
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.path_manager = get_path_manager()
        
        # 信号评估配置
        self.eval_config = config.get("signal_eval_config", {})
        self.enable_signal_evaluation = self.eval_config.get("enable", True)
        self.min_effectiveness_score = self.eval_config.get("min_effectiveness_score", 0.1)
        self.force_reevaluate = self.eval_config.get("force_reevaluate", False)
        
        # 初始化评估器
        self.evaluator = SignalEffectivenessEvaluator(config)
        
        logger.info(f"信号评估工作流初始化完成")
        logger.info(f"启用信号评估: {self.enable_signal_evaluation}")
        logger.info(f"最小有效性分数: {self.min_effectiveness_score}")
    
    def get_evaluation_results_path(self) -> Path:
        """获取评估结果文件路径"""
        session_dir = self.path_manager.get_session_dir()
        return session_dir / "signal_effectiveness_results.csv"
    
    def should_run_evaluation(self) -> bool:
        """判断是否需要运行信号评估"""
        if not self.enable_signal_evaluation:
            return False
        
        if self.force_reevaluate:
            return True
        
        # 检查是否已有评估结果
        results_path = self.get_evaluation_results_path()
        return not results_path.exists()
    
    def run_signal_evaluation(self, symbols: List[str]) -> Optional[List[Dict]]:
        """
        运行信号有效性评估
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            List[Dict]: 评估结果，如果跳过评估则返回None
        """
        if not self.should_run_evaluation():
            logger.info("跳过信号评估，使用现有结果或评估已禁用")
            return self.load_existing_results()
        
        logger.info("开始运行信号有效性评估...")
        
        try:
            # 评估所有信号
            all_results = self.evaluator.evaluate_all_signals(symbols)
            
            if not all_results:
                logger.warning("信号评估未产生任何结果")
                return None
            
            # 聚合结果
            aggregated_results = self.evaluator.aggregate_signal_results(all_results)
            
            # 保存结果
            results_path = self.get_evaluation_results_path()
            self.evaluator.save_results_to_csv(aggregated_results, results_path)
            
            logger.info(f"信号评估完成，结果已保存到: {results_path}")
            logger.info(f"评估了 {len(aggregated_results)} 个信号")
            
            # 显示前10个最有效的信号
            top_signals = sorted(aggregated_results, 
                                key=lambda x: abs(x["effectiveness_score"]), 
                                reverse=True)[:10]
            
            logger.info("前10个最有效的信号:")
            for i, signal in enumerate(top_signals, 1):
                logger.info(f"{i:2d}. {signal['signal_name']:20s} "
                           f"方向:{signal['best_direction']:4s} "
                           f"分数:{signal['effectiveness_score']:8.4f} "
                           f"触发:{signal['total_triggers']:5d}次")
            
            return aggregated_results
            
        except Exception as e:
            logger.error(f"信号评估失败: {e}")
            return None
    
    def load_existing_results(self) -> Optional[List[Dict]]:
        """加载现有的评估结果"""
        results_path = self.get_evaluation_results_path()
        
        if not results_path.exists():
            logger.warning("没有找到现有的评估结果")
            return None
        
        try:
            import pandas as pd
            
            df = pd.read_csv(results_path)
            results = df.to_dict('records')
            
            logger.info(f"加载了 {len(results)} 个信号的评估结果")
            return results
            
        except Exception as e:
            logger.error(f"加载评估结果失败: {e}")
            return None
    
    def get_filtered_signal_pools(self, evaluation_results: List[Dict]) -> tuple:
        """
        根据评估结果获取筛选后的信号池
        
        Args:
            evaluation_results: 评估结果
            
        Returns:
            tuple: (买入信号列表, 卖出信号列表)
        """
        if not evaluation_results:
            logger.warning("评估结果为空，返回空信号池")
            return [], []
        
        # 获取有效的买入和卖出信号
        buy_signals = self.evaluator.get_buy_signals(
            evaluation_results, self.min_effectiveness_score
        )
        sell_signals = self.evaluator.get_sell_signals(
            evaluation_results, self.min_effectiveness_score
        )
        
        logger.info(f"筛选结果: 买入信号 {len(buy_signals)} 个, 卖出信号 {len(sell_signals)} 个")
        
        # 如果信号数量太少，降低阈值
        if len(buy_signals) < 5 or len(sell_signals) < 5:
            logger.warning(f"有效信号数量过少，降低阈值重新筛选")
            lower_threshold = self.min_effectiveness_score * 0.5
            
            buy_signals = self.evaluator.get_buy_signals(
                evaluation_results, lower_threshold
            )
            sell_signals = self.evaluator.get_sell_signals(
                evaluation_results, lower_threshold
            )
            
            logger.info(f"降低阈值后: 买入信号 {len(buy_signals)} 个, 卖出信号 {len(sell_signals)} 个")
        
        return buy_signals, sell_signals
    
    def prepare_signal_pools_for_training(self, symbols: List[str]) -> tuple:
        """
        为训练准备信号池
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            tuple: (买入信号列表, 卖出信号列表)
        """
        logger.info("准备训练用的信号池...")
        
        # 运行信号评估
        evaluation_results = self.run_signal_evaluation(symbols)
        
        if not evaluation_results:
            logger.warning("无法获取信号评估结果，将使用所有信号")
            return [], []  # 返回空列表，表示使用所有信号
        
        # 获取筛选后的信号池
        buy_signals, sell_signals = self.get_filtered_signal_pools(evaluation_results)
        
        # 保存信号池信息到日志
        session_dir = self.path_manager.get_session_dir()
        signal_pools_path = session_dir / "signal_pools.txt"
        
        try:
            with open(signal_pools_path, 'w', encoding='utf-8') as f:
                f.write("=== 买入信号池 ===\n")
                for signal in buy_signals:
                    f.write(f"{signal}\n")
                f.write(f"\n=== 卖出信号池 ===\n")
                for signal in sell_signals:
                    f.write(f"{signal}\n")
            
            logger.info(f"信号池信息已保存到: {signal_pools_path}")
            
        except Exception as e:
            logger.error(f"保存信号池信息失败: {e}")
        
        return buy_signals, sell_signals
    
    def get_evaluation_summary(self) -> Dict:
        """获取评估结果摘要"""
        results = self.load_existing_results()
        
        if not results:
            return {"status": "no_results"}
        
        buy_signals, sell_signals = self.get_filtered_signal_pools(results)
        
        summary = {
            "status": "completed",
            "total_signals_evaluated": len(results),
            "effective_buy_signals": len(buy_signals),
            "effective_sell_signals": len(sell_signals),
            "min_effectiveness_score": self.min_effectiveness_score,
            "results_file": str(self.get_evaluation_results_path())
        }
        
        return summary
