"""
xAutoFactor - 自动因子发现框架
基于遗传编程的交易信号优化系统
"""

__version__ = "1.0.0"
__author__ = "xAutoFactor Team"
__email__ = "<EMAIL>"

# 核心模块
from .core.data_manager import DataManager
from .core.evaluator import Evaluator
from .core.signal_generator import SignalGenerator
from .core.gp_optimizer import GeneticProgrammingOptimizer
from .core.trading_signal_gp_framework import TradingSignalGPFramework

# 训练模块
from .training.training_script import run_training_script
from .training.training_process_manager import TrainingProcessManager

# 监控模块
from .monitoring.monitor import Monitor
from .monitoring.checkpoint_manager import CheckpointManager

# 工具模块
from .utils.reporter import Reporter
from .utils.environment_setup import EnvironmentSetup

# 命令行工具
from .cli import main

__all__ = [
    # 核心模块
    "DataManager",
    "Evaluator",
    "SignalGenerator",
    "GeneticProgrammingOptimizer",
    "TradingSignalGPFramework",
    # 训练模块
    "run_training_script",
    "TrainingProcessManager",
    # 监控模块
    "Monitor",
    "CheckpointManager",
    # 工具模块
    "Reporter",
    "EnvironmentSetup",
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
]
