"""
信号有效性评估器
用于评估单个信号作为买入或卖出信号的有效性
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import csv

from ..utils.logger import get_logger
from .data_manager import DataManager

logger = get_logger(__name__)


class SignalEffectivenessEvaluator:
    """单个信号有效性评估器"""

    def __init__(self, config: Dict):
        """
        初始化评估器

        Args:
            config: 配置参数
        """
        self.config = config
        self.eval_config = config.get("signal_eval_config", {})

        # 评估参数
        self.forward_days = self.eval_config.get("forward_days", 5)  # 向前看几天
        self.success_threshold = self.eval_config.get(
            "success_threshold", 0.002
        )  # 成功阈值0.2%
        self.failure_threshold = self.eval_config.get(
            "failure_threshold", -0.002
        )  # 失败阈值-0.2%
        self.min_signal_count = self.eval_config.get(
            "min_signal_count", 10
        )  # 最少信号数量

        # 权重配置
        self.win_rate_weight = self.eval_config.get("win_rate_weight", 0.4)
        self.avg_return_weight = self.eval_config.get("avg_return_weight", 0.3)
        self.max_return_weight = self.eval_config.get("max_return_weight", 0.2)
        self.max_drawdown_weight = self.eval_config.get("max_drawdown_weight", 0.1)

        # 数据管理器
        self.data_manager = DataManager(config)

        logger.info(f"信号有效性评估器初始化完成")
        logger.info(
            f"评估参数: 向前{self.forward_days}天, 成功阈值{self.success_threshold:.1%}, 失败阈值{self.failure_threshold:.1%}"
        )

    def evaluate_single_signal(self, data: pd.DataFrame, signal_name: str) -> Dict:
        """
        评估单个信号的有效性

        Args:
            data: 包含价格和指标数据的DataFrame
            signal_name: 信号名称

        Returns:
            Dict: 评估结果
        """
        try:
            if signal_name not in data.columns:
                logger.warning(f"信号 {signal_name} 不存在于数据中")
                return self._create_empty_result(signal_name)

            # 获取信号触发点
            signal_triggers = data[data[signal_name] == True].index

            if len(signal_triggers) < self.min_signal_count:
                logger.debug(f"信号 {signal_name} 触发次数过少: {len(signal_triggers)}")
                return self._create_empty_result(signal_name)

            # 计算买入信号效果
            buy_metrics = self._evaluate_signal_direction(data, signal_triggers, "buy")

            # 计算卖出信号效果
            sell_metrics = self._evaluate_signal_direction(
                data, signal_triggers, "sell"
            )

            # 计算综合有效性分数
            buy_score = self._calculate_effectiveness_score(buy_metrics)
            sell_score = self._calculate_effectiveness_score(sell_metrics)

            # 确定最佳方向
            if buy_score > abs(sell_score):
                best_direction = "buy"
                best_score = buy_score
            else:
                best_direction = "sell"
                best_score = -abs(sell_score)  # 卖出信号用负分数表示

            result = {
                "signal_name": signal_name,
                "trigger_count": len(signal_triggers),
                "buy_score": buy_score,
                "sell_score": sell_score,
                "best_direction": best_direction,
                "effectiveness_score": best_score,
                "buy_metrics": buy_metrics,
                "sell_metrics": sell_metrics,
            }

            logger.debug(
                f"信号 {signal_name}: 触发{len(signal_triggers)}次, "
                f"买入分数{buy_score:.4f}, 卖出分数{sell_score:.4f}, "
                f"最佳方向{best_direction}, 有效性分数{best_score:.4f}"
            )

            return result

        except Exception as e:
            logger.error(f"评估信号 {signal_name} 失败: {e}")
            return self._create_empty_result(signal_name)

    def _evaluate_signal_direction(
        self, data: pd.DataFrame, signal_triggers: pd.Index, direction: str
    ) -> Dict:
        """
        评估信号在特定方向上的效果

        Args:
            data: 价格数据
            signal_triggers: 信号触发点
            direction: "buy" 或 "sell"

        Returns:
            Dict: 评估指标
        """
        returns = []
        max_returns = []
        max_drawdowns = []

        for trigger_date in signal_triggers:
            try:
                # 获取触发点在数据中的位置
                trigger_idx = data.index.get_loc(trigger_date)

                # 确保有足够的后续数据
                if trigger_idx + self.forward_days >= len(data):
                    continue

                # 获取触发点和后续价格 - 处理列名大小写问题
                price_col = "close" if "close" in data.columns else "Close"
                if price_col not in data.columns:
                    logger.debug(f"找不到价格列，可用列: {list(data.columns)}")
                    continue

                trigger_price = data.iloc[trigger_idx][price_col]
                future_prices = data.iloc[
                    trigger_idx + 1 : trigger_idx + 1 + self.forward_days
                ][price_col]

                if direction == "buy":
                    # 买入信号：计算收益率
                    period_returns = future_prices / trigger_price - 1
                    max_return = period_returns.max()
                    min_return = period_returns.min()
                    final_return = period_returns.iloc[-1]
                else:
                    # 卖出信号：计算做空收益率
                    period_returns = trigger_price / future_prices - 1
                    max_return = period_returns.max()
                    min_return = period_returns.min()
                    final_return = period_returns.iloc[-1]

                returns.append(final_return)
                max_returns.append(max_return)
                max_drawdowns.append(min_return)

            except Exception as e:
                logger.debug(f"处理触发点 {trigger_date} 失败: {e}")
                continue

        if not returns:
            return self._create_empty_metrics()

        # 计算统计指标
        returns_array = np.array(returns)

        # 胜率计算
        if direction == "buy":
            wins = np.sum(returns_array > self.success_threshold)
            losses = np.sum(returns_array < self.failure_threshold)
        else:
            wins = np.sum(returns_array > self.success_threshold)
            losses = np.sum(returns_array < self.failure_threshold)

        total_signals = wins + losses
        win_rate = wins / total_signals if total_signals > 0 else 0

        metrics = {
            "total_signals": len(returns),
            "valid_signals": total_signals,
            "wins": wins,
            "losses": losses,
            "win_rate": win_rate,
            "avg_return": np.mean(returns_array),
            "std_return": np.std(returns_array),
            "max_return": np.mean(max_returns),
            "max_drawdown": np.mean(max_drawdowns),
            "sharpe_ratio": (
                np.mean(returns_array) / np.std(returns_array)
                if np.std(returns_array) > 0
                else 0
            ),
        }

        return metrics

    def _calculate_effectiveness_score(self, metrics: Dict) -> float:
        """
        计算有效性分数

        Args:
            metrics: 评估指标

        Returns:
            float: 有效性分数
        """
        if metrics["valid_signals"] == 0:
            return 0.0

        # 加权计算有效性分数
        score = (
            self.win_rate_weight * metrics["win_rate"]
            + self.avg_return_weight * metrics["avg_return"] * 100  # 转换为百分比
            + self.max_return_weight * metrics["max_return"] * 100
            + self.max_drawdown_weight
            * (-metrics["max_drawdown"])
            * 100  # 回撤是负向指标
        )

        return score

    def _create_empty_result(self, signal_name: str) -> Dict:
        """创建空的评估结果"""
        return {
            "signal_name": signal_name,
            "trigger_count": 0,
            "buy_score": 0.0,
            "sell_score": 0.0,
            "best_direction": "none",
            "effectiveness_score": 0.0,
            "buy_metrics": self._create_empty_metrics(),
            "sell_metrics": self._create_empty_metrics(),
        }

    def _create_empty_metrics(self) -> Dict:
        """创建空的指标字典"""
        return {
            "total_signals": 0,
            "valid_signals": 0,
            "wins": 0,
            "losses": 0,
            "win_rate": 0.0,
            "avg_return": 0.0,
            "std_return": 0.0,
            "max_return": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
        }

    def evaluate_all_signals(self, symbols: List[str]) -> List[Dict]:
        """
        评估所有信号的有效性

        Args:
            symbols: 股票代码列表

        Returns:
            List[Dict]: 所有信号的评估结果
        """
        logger.info(f"开始评估所有信号的有效性，股票数量: {len(symbols)}")

        # 获取所有布尔指标
        boolean_indicators = self.data_manager.get_boolean_indicators()
        logger.info(f"待评估信号数量: {len(boolean_indicators)}")

        all_results = []

        for symbol in symbols:
            try:
                logger.info(f"评估股票 {symbol} 的信号...")

                # 获取股票数据 - 使用配置中的日期范围
                train_start = self.config.get("train_start", "2020-01-01")
                train_end = self.config.get("train_end", "2023-12-31")

                data = self.data_manager.load_data(symbol, train_start, train_end)
                if data is None or len(data) == 0:
                    logger.warning(f"无法获取股票 {symbol} 的数据")
                    continue

                # 计算技术指标
                data = self.data_manager.add_technical_indicators(data)

                # 添加布尔指标
                data = self.data_manager.add_boolean_indicators(data)

                # 评估每个信号
                for signal_name in boolean_indicators:
                    result = self.evaluate_single_signal(data, signal_name)
                    result["symbol"] = symbol
                    all_results.append(result)

            except Exception as e:
                logger.error(f"评估股票 {symbol} 失败: {e}")
                continue

        logger.info(f"信号评估完成，总计评估了 {len(all_results)} 个信号-股票组合")
        return all_results

    def aggregate_signal_results(self, all_results: List[Dict]) -> List[Dict]:
        """
        聚合多个股票的信号评估结果

        Args:
            all_results: 所有评估结果

        Returns:
            List[Dict]: 聚合后的信号评估结果
        """
        logger.info("开始聚合信号评估结果...")

        # 按信号名称分组
        signal_groups = {}
        for result in all_results:
            signal_name = result["signal_name"]
            if signal_name not in signal_groups:
                signal_groups[signal_name] = []
            signal_groups[signal_name].append(result)

        aggregated_results = []

        for signal_name, results in signal_groups.items():
            # 过滤掉无效结果
            valid_results = [r for r in results if r["trigger_count"] > 0]

            if not valid_results:
                logger.debug(f"信号 {signal_name} 没有有效结果")
                continue

            # 计算平均指标
            avg_buy_score = np.mean([r["buy_score"] for r in valid_results])
            avg_sell_score = np.mean([r["sell_score"] for r in valid_results])
            total_triggers = sum([r["trigger_count"] for r in valid_results])

            # 确定最佳方向
            if avg_buy_score > abs(avg_sell_score):
                best_direction = "buy"
                effectiveness_score = avg_buy_score
            else:
                best_direction = "sell"
                effectiveness_score = -abs(avg_sell_score)

            aggregated_result = {
                "signal_name": signal_name,
                "total_triggers": total_triggers,
                "avg_buy_score": avg_buy_score,
                "avg_sell_score": avg_sell_score,
                "best_direction": best_direction,
                "effectiveness_score": effectiveness_score,
                "evaluated_symbols": len(valid_results),
                "symbol_results": valid_results,
            }

            aggregated_results.append(aggregated_result)

        # 按有效性分数排序
        aggregated_results.sort(
            key=lambda x: abs(x["effectiveness_score"]), reverse=True
        )

        logger.info(f"信号聚合完成，有效信号数量: {len(aggregated_results)}")
        return aggregated_results

    def save_results_to_csv(self, results: List[Dict], output_path: Path):
        """
        将评估结果保存为CSV文件

        Args:
            results: 评估结果列表
            output_path: 输出文件路径
        """
        try:
            # 确保目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 准备CSV数据
            csv_data = []
            for result in results:
                csv_row = {
                    "signal_name": result["signal_name"],
                    "total_triggers": result["total_triggers"],
                    "avg_buy_score": round(result["avg_buy_score"], 6),
                    "avg_sell_score": round(result["avg_sell_score"], 6),
                    "best_direction": result["best_direction"],
                    "effectiveness_score": round(result["effectiveness_score"], 6),
                    "evaluated_symbols": result["evaluated_symbols"],
                }
                csv_data.append(csv_row)

            # 写入CSV文件
            with open(output_path, "w", newline="", encoding="utf-8") as csvfile:
                fieldnames = [
                    "signal_name",
                    "total_triggers",
                    "avg_buy_score",
                    "avg_sell_score",
                    "best_direction",
                    "effectiveness_score",
                    "evaluated_symbols",
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(csv_data)

            logger.info(f"评估结果已保存到: {output_path}")

        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
            raise

    def get_buy_signals(self, results: List[Dict], min_score: float = 0.0) -> List[str]:
        """
        获取适合做买入信号的信号列表

        Args:
            results: 评估结果
            min_score: 最小有效性分数阈值

        Returns:
            List[str]: 买入信号列表
        """
        buy_signals = []
        for result in results:
            if (
                result["best_direction"] == "buy"
                and result["effectiveness_score"] > min_score
            ):
                buy_signals.append(result["signal_name"])

        logger.info(f"找到 {len(buy_signals)} 个有效买入信号 (分数 > {min_score})")
        return buy_signals

    def get_sell_signals(
        self, results: List[Dict], min_score: float = 0.0
    ) -> List[str]:
        """
        获取适合做卖出信号的信号列表

        Args:
            results: 评估结果
            min_score: 最小有效性分数阈值（注意卖出信号分数为负）

        Returns:
            List[str]: 卖出信号列表
        """
        sell_signals = []
        for result in results:
            if (
                result["best_direction"] == "sell"
                and result["effectiveness_score"] < -min_score
            ):
                sell_signals.append(result["signal_name"])

        logger.info(f"找到 {len(sell_signals)} 个有效卖出信号 (分数 < -{min_score})")
        return sell_signals
