#!/usr/bin/env python3
"""
xAutoFactor 安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "xAutoFactor - 自动因子发现框架"

# 读取requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), "requirements.txt")
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="xAutoFactor",
    version="1.0.0",
    author="xAutoFactor Team",
    author_email="<EMAIL>",
    description="基于遗传编程的交易信号优化系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/xautofactor/xautofactor",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "monitoring": [
            "flask>=2.0",
            "psutil>=5.8",
        ],
    },
    entry_points={
        "console_scripts": [
            "auto_factor=xAutoFactor.cli.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "xAutoFactor": ["*.json", "*.yaml", "*.yml"],
    },
    zip_safe=False,
)
