# evaluator.py - 信号评估器模块
"""
评估交易信号的表现，计算各种财务指标
包括夏普比率、卡玛比率、最大回撤、年化收益等
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from loguru import logger
import warnings
warnings.filterwarnings('ignore')

class Evaluator:
    """交易信号评估器"""
    
    def __init__(self, config: Dict):
        """
        初始化评估器
        
        Args:
            config (dict): 评估配置参数
        """
        self.config = config
        self.transaction_cost = config.get('transaction_cost', 0.001)  # 0.1%交易成本
        self.initial_capital = config.get('initial_capital', 100000)   # 初始资金10万
        self.risk_free_rate = config.get('risk_free_rate', 0.02)       # 无风险利率2%
        
        # 存储最近一次的交易记录
        self.last_trade_records = []
        self.last_portfolio_values = []
        
    def evaluate_signals(self, data: pd.DataFrame, signals: pd.Series) -> Optional[Dict]:
        """
        评估交易信号的表现
        
        Args:
            data (pd.DataFrame): 价格数据
            signals (pd.Series): 交易信号 (1=买入, -1=卖出, 0=持有)
            
        Returns:
            dict: 评估指标字典，如果失败返回None
        """
        try:
            if len(signals) == 0:
                logger.debug("信号序列为空")
                return None
                
            # 添加调试日志
            signal_counts = signals.value_counts()
            logger.debug(f"信号分布: {signal_counts.to_dict()}")
            
            # 生成交易记录
            trade_records = self._generate_trades(data, signals)
            
            if not trade_records:
                logger.debug("没有生成任何交易记录")
                return None
            
            logger.debug(f"生成了 {len(trade_records)} 个交易记录")
            
            # 计算组合价值序列
            portfolio_values = self._calculate_portfolio_values(data, signals)
            
            # 计算收益序列
            returns = self._calculate_returns(portfolio_values)
            
            # 计算各项指标
            metrics = self._calculate_metrics(returns, trade_records, data)
            
            # 保存交易记录用于详细分析
            self.last_trade_records = trade_records
            self.last_portfolio_values = portfolio_values
            
            return metrics
            
        except Exception as e:
            logger.error(f"评估信号失败: {e}")
            return None
    
    def _generate_trades(self, data: pd.DataFrame, signals: pd.Series) -> List[Dict]:
        """生成交易记录"""
        trades = []
        position = 0  # 0=空仓, 1=持有
        entry_price = 0
        entry_date = None
        
        # 添加调试日志
        buy_signals = (signals == 1).sum()
        sell_signals = (signals == -1).sum()
        logger.debug(f"交易生成: 买入信号={buy_signals}, 卖出信号={sell_signals}")
        
        for i, (date, signal) in enumerate(signals.items()):
            current_price = data.loc[date, 'Close']
            
            # 买入信号且当前空仓
            if signal == 1 and position == 0:
                position = 1
                entry_price = current_price
                entry_date = date
                # logger.debug(f"买入信号: {date}, 价格={current_price}")
                
            # 卖出信号且当前持仓
            elif signal == -1 and position == 1:
                exit_price = current_price
                exit_date = date
                
                # 计算收益
                gross_return = (exit_price - entry_price) / entry_price
                net_return = gross_return - 2 * self.transaction_cost  # 买入卖出各一次成本
                
                trade_record = {
                    'entry_date': entry_date,
                    'exit_date': exit_date,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'gross_return': gross_return,
                    'net_return': net_return,
                    'holding_days': (exit_date - entry_date).days,
                    'profit_loss': net_return * self.initial_capital
                }
                
                trades.append(trade_record)
                # logger.debug(f"完成交易: {entry_date} -> {exit_date}, 收益={net_return:.4f}")
                
                position = 0
                entry_price = 0
                entry_date = None
        
        logger.debug(f"总共生成 {len(trades)} 个交易")
        return trades
    
    def _calculate_portfolio_values(self, data: pd.DataFrame, signals: pd.Series) -> pd.Series:
        """计算组合价值序列"""
        portfolio_values = pd.Series(index=data.index, dtype=float)
        portfolio_values.iloc[0] = self.initial_capital
        
        position = 0
        shares = 0
        cash = self.initial_capital
        
        for i, (date, signal) in enumerate(signals.items()):
            current_price = data.loc[date, 'Close']
            
            if i == 0:
                portfolio_values.loc[date] = self.initial_capital
                continue
                
            # 买入信号
            if signal == 1 and position == 0:
                # 全仓买入
                transaction_cost = cash * self.transaction_cost
                shares = (cash - transaction_cost) / current_price
                cash = 0
                position = 1
                
            # 卖出信号
            elif signal == -1 and position == 1:
                # 全部卖出
                cash = shares * current_price
                transaction_cost = cash * self.transaction_cost
                cash -= transaction_cost
                shares = 0
                position = 0
            
            # 计算当前组合价值
            if position == 1:
                portfolio_values.loc[date] = shares * current_price
            else:
                portfolio_values.loc[date] = cash
                
        return portfolio_values
    
    def _calculate_returns(self, portfolio_values: pd.Series) -> pd.Series:
        """计算收益率序列"""
        returns = portfolio_values.pct_change().dropna()
        return returns
    
    def _calculate_metrics(self, returns: pd.Series, trades: List[Dict], data: pd.DataFrame) -> Dict:
        """计算各项评估指标"""
        metrics = {}
        
        # 基础统计
        metrics['total_trades'] = len(trades)
        
        if len(trades) == 0:
            # 没有交易的情况
            metrics.update({
                'annual_return': 0.0,
                'total_return': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'calmar_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'recovery_factor': 0.0
            })
            return metrics
        
        # 收益相关指标
        if len(self.last_portfolio_values) > 0:
            total_return = (self.last_portfolio_values.iloc[-1] / self.initial_capital) - 1
        else:
            total_return = 0.0
        metrics['total_return'] = total_return
        
        # 年化收益率
        trading_days = len(data)
        years = trading_days / 252.0
        if years > 0:
            annual_return = (1 + total_return) ** (1/years) - 1
        else:
            annual_return = 0.0
        metrics['annual_return'] = annual_return
        
        # 波动率
        if len(returns) > 1:
            volatility = returns.std() * np.sqrt(252)
        else:
            volatility = 0.0
        metrics['volatility'] = volatility
        
        # 夏普比率
        if volatility > 0:
            excess_return = annual_return - self.risk_free_rate
            sharpe_ratio = excess_return / volatility
        else:
            sharpe_ratio = 0.0
        metrics['sharpe_ratio'] = sharpe_ratio
        
        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(self.last_portfolio_values)
        metrics['max_drawdown'] = max_drawdown
        
        # 卡玛比率
        if abs(max_drawdown) > 1e-6:
            calmar_ratio = annual_return / abs(max_drawdown)
        else:
            calmar_ratio = 0.0
        metrics['calmar_ratio'] = calmar_ratio
        
        # 交易相关指标
        trade_metrics = self._calculate_trade_metrics(trades)
        metrics.update(trade_metrics)
        
        # 恢复因子
        if abs(max_drawdown) > 1e-6:
            recovery_factor = total_return / abs(max_drawdown)
        else:
            recovery_factor = 0.0
        metrics['recovery_factor'] = recovery_factor
        
        return metrics
    
    def _calculate_max_drawdown(self, portfolio_values: pd.Series) -> float:
        """计算最大回撤"""
        try:
            cumulative = portfolio_values / portfolio_values.iloc[0]
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min()
            return max_drawdown
        except:
            return 0.0
    
    def _calculate_trade_metrics(self, trades: List[Dict]) -> Dict:
        """计算交易相关指标"""
        if not trades:
            return {
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'avg_holding_days': 0.0,
                'total_profit': 0.0,
                'total_loss': 0.0
            }
        
        # 提取收益数据
        returns = [trade['net_return'] for trade in trades]
        
        # 盈亏分离
        winning_trades = [r for r in returns if r > 0]
        losing_trades = [r for r in returns if r < 0]
        
        # 胜率
        win_rate = len(winning_trades) / len(trades) if trades else 0.0
        
        # 平均盈利和亏损
        avg_win = np.mean(winning_trades) if winning_trades else 0.0
        avg_loss = np.mean(losing_trades) if losing_trades else 0.0
        
        # 盈利因子
        total_profit = sum(winning_trades)
        total_loss = abs(sum(losing_trades))
        profit_factor = total_profit / total_loss if total_loss > 0 else 0.0
        
        # 平均持仓天数
        holding_days = [trade['holding_days'] for trade in trades]
        avg_holding_days = np.mean(holding_days) if holding_days else 0.0
        
        return {
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_holding_days': avg_holding_days,
            'total_profit': total_profit,
            'total_loss': total_loss
        }
    
    def get_trade_records(self) -> List[Dict]:
        """获取最近一次评估的交易记录"""
        return self.last_trade_records.copy()
    
    def get_portfolio_values(self) -> pd.Series:
        """获取最近一次评估的组合价值序列"""
        return self.last_portfolio_values.copy()
    
    def generate_performance_report(self, metrics: Dict, symbol: str = "") -> str:
        """生成表现报告"""
        report = f"""
=== 交易信号表现报告 {symbol} ===

基础指标:
- 总交易次数: {metrics.get('total_trades', 0)}
- 总收益率: {metrics.get('total_return', 0):.2%}
- 年化收益率: {metrics.get('annual_return', 0):.2%}
- 波动率: {metrics.get('volatility', 0):.2%}

风险调整收益:
- 夏普比率: {metrics.get('sharpe_ratio', 0):.3f}
- 卡玛比率: {metrics.get('calmar_ratio', 0):.3f}
- 恢复因子: {metrics.get('recovery_factor', 0):.3f}

风险指标:
- 最大回撤: {metrics.get('max_drawdown', 0):.2%}

交易指标:
- 胜率: {metrics.get('win_rate', 0):.2%}
- 平均盈利: {metrics.get('avg_win', 0):.2%}
- 平均亏损: {metrics.get('avg_loss', 0):.2%}
- 盈利因子: {metrics.get('profit_factor', 0):.3f}
- 平均持仓天数: {metrics.get('avg_holding_days', 0):.1f}

"""
        return report
    
    def calculate_rolling_metrics(self, window_days: int = 252) -> Dict:
        """计算滚动指标"""
        if not self.last_portfolio_values or len(self.last_portfolio_values) < window_days:
            return {}
        
        portfolio_values = pd.Series(self.last_portfolio_values)
        returns = portfolio_values.pct_change().dropna()
        
        rolling_metrics = {}
        
        # 滚动夏普比率
        rolling_sharpe = returns.rolling(window=window_days).apply(
            lambda x: (x.mean() * 252 - self.risk_free_rate) / (x.std() * np.sqrt(252))
        )
        rolling_metrics['rolling_sharpe'] = rolling_sharpe
        
        # 滚动波动率
        rolling_vol = returns.rolling(window=window_days).std() * np.sqrt(252)
        rolling_metrics['rolling_volatility'] = rolling_vol
        
        # 滚动最大回撤
        rolling_dd = portfolio_values.rolling(window=window_days).apply(
            lambda x: self._calculate_max_drawdown(pd.Series(x))
        )
        rolling_metrics['rolling_max_drawdown'] = rolling_dd
        
        return rolling_metrics
    
    def compare_to_benchmark(self, benchmark_data: pd.DataFrame, 
                           benchmark_column: str = 'Close') -> Dict:
        """与基准比较"""
        if not self.last_portfolio_values:
            return {}
        
        try:
            # 计算基准收益
            benchmark_returns = benchmark_data[benchmark_column].pct_change().dropna()
            portfolio_returns = pd.Series(self.last_portfolio_values).pct_change().dropna()
            
            # 对齐时间序列
            common_index = portfolio_returns.index.intersection(benchmark_returns.index)
            if len(common_index) == 0:
                return {}
            
            portfolio_aligned = portfolio_returns.loc[common_index]
            benchmark_aligned = benchmark_returns.loc[common_index]
            
            # 计算比较指标
            comparison = {}
            
            # 累计收益比较
            portfolio_cumret = (1 + portfolio_aligned).cumprod() - 1
            benchmark_cumret = (1 + benchmark_aligned).cumprod() - 1
            
            comparison['portfolio_total_return'] = portfolio_cumret.iloc[-1]
            comparison['benchmark_total_return'] = benchmark_cumret.iloc[-1]
            comparison['excess_return'] = portfolio_cumret.iloc[-1] - benchmark_cumret.iloc[-1]
            
            # 波动率比较
            portfolio_vol = portfolio_aligned.std() * np.sqrt(252)
            benchmark_vol = benchmark_aligned.std() * np.sqrt(252)
            
            comparison['portfolio_volatility'] = portfolio_vol
            comparison['benchmark_volatility'] = benchmark_vol
            
            # 信息比率
            excess_returns = portfolio_aligned - benchmark_aligned
            tracking_error = excess_returns.std() * np.sqrt(252)
            information_ratio = excess_returns.mean() * 252 / tracking_error if tracking_error > 0 else 0
            
            comparison['information_ratio'] = information_ratio
            comparison['tracking_error'] = tracking_error
            
            # Beta
            covariance = np.cov(portfolio_aligned, benchmark_aligned)[0][1]
            benchmark_variance = np.var(benchmark_aligned)
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
            
            comparison['beta'] = beta
            
            # Alpha
            portfolio_annual_return = portfolio_aligned.mean() * 252
            benchmark_annual_return = benchmark_aligned.mean() * 252
            alpha = portfolio_annual_return - (self.risk_free_rate + beta * (benchmark_annual_return - self.risk_free_rate))
            
            comparison['alpha'] = alpha
            
            return comparison
            
        except Exception as e:
            logger.error(f"基准比较失败: {e}")
            return {}


class PerformanceAnalyzer:
    """性能分析器，提供更深入的分析功能"""
    
    def __init__(self):
        logger = logging.getLogger(__name__)
    
    def analyze_drawdown_periods(self, portfolio_values: pd.Series) -> List[Dict]:
        """分析回撤期间"""
        try:
            cumulative = portfolio_values / portfolio_values.iloc[0]
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            
            # 找到回撤期间
            drawdown_periods = []
            in_drawdown = False
            start_date = None
            max_dd = 0
            
            for date, dd in drawdown.items():
                if dd < -0.001 and not in_drawdown:  # 开始回撤
                    in_drawdown = True
                    start_date = date
                    max_dd = dd
                elif dd < max_dd and in_drawdown:  # 更新最大回撤
                    max_dd = dd
                elif dd >= -0.001 and in_drawdown:  # 回撤结束
                    end_date = date
                    recovery_days = (end_date - start_date).days
                    
                    drawdown_periods.append({
                        'start_date': start_date,
                        'end_date': end_date,
                        'max_drawdown': max_dd,
                        'recovery_days': recovery_days
                    })
                    
                    in_drawdown = False
                    start_date = None
                    max_dd = 0
            
            return drawdown_periods
            
        except Exception as e:
            logger.error(f"回撤期间分析失败: {e}")
            return []
    
    def calculate_var_cvar(self, returns: pd.Series, confidence_level: float = 0.05) -> Dict:
        """计算VaR和CVaR"""
        try:
            if len(returns) == 0:
                return {'var': 0, 'cvar': 0}
            
            # VaR
            var = np.percentile(returns, confidence_level * 100)
            
            # CVaR (条件VaR)
            cvar = returns[returns <= var].mean()
            
            return {
                'var': var,
                'cvar': cvar,
                'var_annual': var * np.sqrt(252),
                'cvar_annual': cvar * np.sqrt(252)
            }
            
        except Exception as e:
            logger.error(f"VaR/CVaR计算失败: {e}")
            return {'var': 0, 'cvar': 0}
    
    def analyze_monthly_returns(self, portfolio_values: pd.Series) -> pd.DataFrame:
        """分析月度收益"""
        try:
            returns = portfolio_values.pct_change().dropna()
            
            # 按月份分组
            monthly_returns = returns.groupby([returns.index.year, returns.index.month]).apply(
                lambda x: (1 + x).prod() - 1
            )
            
            # 创建月度收益矩阵
            monthly_df = monthly_returns.reset_index()
            monthly_df.columns = ['Year', 'Month', 'Return']
            
            # 透视表
            monthly_pivot = monthly_df.pivot(index='Year', columns='Month', values='Return')
            monthly_pivot.columns = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            
            # 添加年度收益
            monthly_pivot['Annual'] = (1 + monthly_pivot).prod(axis=1) - 1
            
            return monthly_pivot
            
        except Exception as e:
            logger.error(f"月度收益分析失败: {e}")
            return pd.DataFrame()
    
    def calculate_trade_statistics(self, trades: List[Dict]) -> Dict:
        """详细的交易统计"""
        if not trades:
            return {}
        
        returns = [trade['net_return'] for trade in trades]
        holding_days = [trade['holding_days'] for trade in trades]
        
        stats = {
            'total_trades': len(trades),
            'winning_trades': len([r for r in returns if r > 0]),
            'losing_trades': len([r for r in returns if r < 0]),
            'win_rate': len([r for r in returns if r > 0]) / len(returns),
            
            # 收益统计
            'best_trade': max(returns),
            'worst_trade': min(returns),
            'avg_return': np.mean(returns),
            'median_return': np.median(returns),
            'std_return': np.std(returns),
            
            # 持仓时间统计
            'avg_holding_days': np.mean(holding_days),
            'median_holding_days': np.median(holding_days),
            'min_holding_days': min(holding_days),
            'max_holding_days': max(holding_days),
            
            # 连续交易统计
            'max_consecutive_wins': self._max_consecutive(returns, lambda x: x > 0),
            'max_consecutive_losses': self._max_consecutive(returns, lambda x: x < 0),
        }
        
        return stats
    
    def _max_consecutive(self, returns: List[float], condition_func) -> int:
        """计算最大连续次数"""
        max_consecutive = 0
        current_consecutive = 0
        
        for ret in returns:
            if condition_func(ret):
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive